# Implementation Guide: Fixing SAITS/BRITS R² Discrepancy

## Quick Summary

The deep learning models (SAITS/BRITS) show perfect R² (1.0) for imputation but poor R² (<0.8) for prediction because they are being evaluated on two fundamentally different tasks:

1. **Imputation**: Filling artificially created gaps with surrounding context available
2. **Prediction**: Predicting all values without any target context

## Root Cause

The issue is in `ml_core.py`, specifically in the `impute_logs_deep` function:

- **Lines 1291-1319**: Evaluation only on artificially missing values (imputation task)
- **Lines 965-994**: `prepare_prediction_data` masks the ENTIRE target column for prediction
- **reporting.py**: Evaluates on ALL overlapping original/predicted values (prediction task)

## Immediate Fix Options

### Option 1: Minimal Code Change (Recommended for Quick Fix)

Replace lines 1279-1343 in `ml_core.py` with the enhanced evaluation code from `ml_core_evaluation_fix.py`. This will:

1. Keep existing imputation evaluation
2. Add separate prediction evaluation
3. Report both metrics transparently
4. Warn users about performance discrepancies

### Option 2: Comprehensive Solution

1. Integrate `deep_learning_evaluation_fix.py` functions
2. Modify `impute_logs_deep` to use `evaluate_imputation_and_prediction`
3. Update reporting to show both imputation and prediction metrics
4. Add model selection guidance based on task type

## Step-by-Step Implementation

### Step 1: Backup Current Code
```bash
cp ml_core.py ml_core_backup.py
cp reporting.py reporting_backup.py
```

### Step 2: Add New Evaluation Module
```bash
# The deep_learning_evaluation_fix.py file is already created
# It contains proper evaluation functions for both tasks
```

### Step 3: Modify ml_core.py

Add import at the top:
```python
from deep_learning_evaluation_fix import evaluate_imputation_and_prediction
```

Replace the evaluation section (lines 1279-1343) with the code from `ml_core_evaluation_fix.py`.

### Step 4: Update Reporting

In `reporting.py`, modify the report generation to show both metrics:

```python
def generate_enhanced_model_report(model_results):
    """Generate report showing both imputation and prediction performance."""
    
    for result in model_results:
        if 'comprehensive_evaluation' in result:
            eval_data = result['comprehensive_evaluation']
            
            print(f"\nModel: {result['model_name']}")
            print("="*50)
            
            # Imputation Performance
            if 'imputation_metrics' in eval_data:
                imp = eval_data['imputation_metrics']
                print(f"Imputation Task (Filling Gaps):")
                print(f"  R²: {imp['r2']:.4f}, MAE: {imp['mae']:.4f}")
            
            # Prediction Performance
            if 'prediction_metrics' in eval_data:
                pred = eval_data['prediction_metrics']
                print(f"Prediction Task (No Context):")
                print(f"  R²: {pred['r2']:.4f}, MAE: {pred['mae']:.4f}")
            
            # Discrepancy Analysis
            if 'combined_metrics' in eval_data:
                cm = eval_data['combined_metrics']
                print(f"Performance Analysis:")
                print(f"  R² Drop: {cm['r2_discrepancy']:.4f}")
                if cm['r2_discrepancy'] > 0.2:
                    print("  ⚠️ Model optimized for imputation, not prediction")
```

## Expected Results After Fix

### Before Fix:
- SAITS/BRITS report single R² (~1.0) from imputation evaluation
- Poor performance when actually used for prediction
- Confusion about model capabilities

### After Fix:
- Clear separation of metrics:
  - Imputation R²: ~0.95-1.0 (filling gaps)
  - Prediction R²: ~0.70-0.80 (no context)
- Transparent performance reporting
- Proper model selection guidance

## Best Practices Moving Forward

1. **For Imputation Tasks** (filling missing values in existing data):
   - Use SAITS/BRITS models
   - Report imputation metrics

2. **For Prediction Tasks** (forecasting without historical target data):
   - Use XGBoost/CatBoost/LightGBM
   - Report prediction metrics

3. **Model Selection**:
   ```python
   if task == "fill_missing_values":
       use_model = "SAITS" if imputation_r2 > 0.9 else "XGBoost"
   elif task == "predict_future_values":
       use_model = "XGBoost"  # Better for pure prediction
   ```

## Testing the Fix

```python
# Test script to verify the fix works correctly
def test_evaluation_fix():
    # Load sample data
    df = load_test_data()
    
    # Run model with new evaluation
    results = impute_logs_deep_with_fixed_evaluation(
        df, feature_cols, target_col, model_config, hparams
    )
    
    # Check that both metrics are present
    assert 'imputation_metrics' in results
    assert 'prediction_metrics' in results
    
    # Verify discrepancy is detected
    imp_r2 = results['imputation_metrics']['r2']
    pred_r2 = results['prediction_metrics']['r2']
    
    print(f"Imputation R²: {imp_r2:.4f}")
    print(f"Prediction R²: {pred_r2:.4f}")
    print(f"Discrepancy: {imp_r2 - pred_r2:.4f}")
```

## Summary

The fix properly evaluates deep learning models on both imputation and prediction tasks, revealing that SAITS/BRITS are specialized imputation models that struggle with pure prediction. This transparency allows users to select the right model for their specific task.