# Deep Learning Models R² Discrepancy Analysis

## Executive Summary

The SAITS and BRITS models show a significant discrepancy in R² performance:
- **Perfect R² (1.0)** when evaluating imputation on artificially missing values
- **Poor R² (<0.8)** when comparing original vs predicted values in real-world scenarios

This discrepancy indicates a fundamental difference in how the models are evaluated during training vs production use.

## Root Cause Analysis

### 1. Two Different Evaluation Scenarios

The codebase evaluates model performance in two distinct ways:

#### A. Imputation Evaluation (Perfect R² = 1.0)
- **Location**: `ml_core.py`, lines 1291-1319 in `impute_logs_deep()`
- **Method**: Only evaluates on artificially introduced missing values
- **Process**:
  ```python
  # Only evaluate on artificially missing values
  val_mask = torch.isnan(val_train_tensor[:, :, target_idx])
  y_pred_val = imputed_val_tensor[:, :, target_idx][val_mask]
  y_true_val = val_truth_tensor[:, :, target_idx][val_mask]
  ```
- **Why R² is Perfect**: The model is trained specifically to reconstruct artificially masked values, which it has seen patterns for during training

#### B. Prediction Evaluation (Poor R² < 0.8)
- **Location**: `reporting.py`, lines 271-272, 336-337
- **Method**: Compares original data with model predictions on ALL data points
- **Process**:
  ```python
  comp = d[[tgt, pred_col]].dropna()
  mae = mean_absolute_error(comp[tgt], comp[pred_col])
  r2 = r2_score(comp[tgt], comp[pred_col])
  ```
- **Why R² is Poor**: Evaluates the model's ability to predict unseen patterns and extrapolate beyond training data

### 2. Critical Implementation Issues

#### Issue 1: Evaluation Methodology Mismatch
The training evaluation (lines 1291-1319) only tests the model's ability to fill in artificially created gaps, not its ability to predict future values or handle real missing data patterns.

#### Issue 2: Data Preparation for Prediction
In `prepare_prediction_data()` (lines 965-994):
```python
# Mask the *entire* target column so the model cannot peek at ground truth
prediction_input_df[target_col] = np.nan
```
This creates a scenario where the model must predict ALL values without any context from the target variable itself, which is much harder than imputation with surrounding context.

#### Issue 3: Different Task Complexity
- **Imputation Task**: Fill gaps with surrounding context available
- **Prediction Task**: Extrapolate or predict without target context

### 3. Comparison with Shallow Models

Shallow models (XGBoost, CatBoost, LightGBM) perform better because:

1. **Direct Feature-Target Mapping**: They learn direct relationships between features and target
2. **No Temporal Dependencies**: They don't rely on sequential patterns in the target variable
3. **Simpler Evaluation**: Same task for training and evaluation

From `impute_logs()` (lines 943-944):
```python
X_pred = res.loc[pred_mask, feat_set].ffill().bfill()
preds = best_model.predict(X_pred)
```

### 4. Specific SAITS/BRITS Architecture Issues

These models are designed for **imputation**, not **prediction**:
- They excel at using bidirectional context to fill missing values
- They struggle when the entire target column is missing (prediction scenario)
- Their architecture assumes some known values exist to anchor the imputation

## Evidence of the Issue

### 1. Warning Messages
The code itself acknowledges this issue (lines 1322-1325):
```python
if r2 > 0.95:
    print("🚨 CRITICAL WARNING: Suspiciously high R² (>0.95) - potential data leakage detected!")
```

### 2. Different Evaluation Contexts
- **Validation** (during training): Only on artificial gaps
- **Production** (in reporting): On all overlapping original/predicted values

### 3. Task Mismatch
The models are trained for imputation but evaluated for prediction - fundamentally different tasks.

## Recommendations

### 1. Immediate Fixes

#### A. Align Evaluation Metrics
Evaluate models on the same task they'll be used for:
```python
# During validation, evaluate on prediction task, not just imputation
# Create held-out wells or time periods for true prediction evaluation
```

#### B. Separate Imputation vs Prediction Models
Clearly distinguish between:
- Models for filling missing values (imputation)
- Models for predicting future values (prediction)

### 2. Code Modifications

#### A. Fix Validation Evaluation
```python
# In impute_logs_deep, add prediction-style evaluation
# Evaluate on completely held-out sequences, not just artificial gaps
test_sequences_for_prediction = create_prediction_sequences(test_df)
pred_eval_results = evaluate_prediction_performance(model, test_sequences_for_prediction)
```

#### B. Implement Proper Train/Test Split
```python
# Use temporal or spatial splits that reflect real use cases
# Don't mix imputation and prediction evaluations
```

### 3. Architecture Recommendations

#### A. For Imputation Tasks
- Continue using SAITS/BRITS with current evaluation
- Be transparent about evaluation metrics

#### B. For Prediction Tasks
- Consider hybrid approaches that combine shallow model strengths
- Modify deep learning architectures to handle full prediction scenarios
- Add encoder-only architectures that don't rely on target context

### 4. Reporting Improvements

Clearly separate metrics:
```python
metrics = {
    'imputation_metrics': {
        'artificial_missing_r2': 0.99,  # On artificial gaps
        'natural_missing_r2': 0.85      # On natural missing data
    },
    'prediction_metrics': {
        'future_value_r2': 0.75,        # True prediction
        'interpolation_r2': 0.90        # Within known range
    }
}
```

## Conclusion

The R² discrepancy is not a bug but a fundamental mismatch between:
1. How the models are trained (imputation on artificial gaps)
2. How they are evaluated in production (prediction of all values)

SAITS and BRITS are excellent imputation models but struggle with pure prediction tasks. The codebase should either:
1. Use these models only for imputation and clearly state this limitation
2. Modify the architecture and training process for prediction tasks
3. Implement separate evaluation metrics for imputation vs prediction performance

The shallow models outperform deep models in prediction because they're designed for this task, while SAITS/BRITS are designed for imputation with bidirectional context.