#!/usr/bin/env python3
"""
Quick fix for the broadcasting error in SAITS and BRITS models.
This script directly modifies the problematic lines in ml_core.py.
"""

def apply_quick_fix():
    """Apply a quick fix to the broadcasting error."""
    
    print("🔧 Applying Quick Broadcasting Fix...")
    
    try:
        # Read the file
        with open('ml_core.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Track changes
        changes_made = 0
        
        # Fix the prediction evaluation section (around line 1332-1333)
        for i, line in enumerate(lines):
            # Fix the flatten() calls
            if 'y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy().flatten()' in line:
                lines[i] = '        y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy()\n'
                changes_made += 1
                print(f"✅ Fixed line {i+1}: Removed flatten() from y_pred_prediction")
                
            elif 'y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy().flatten()' in line:
                lines[i] = '        y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy()\n'
                # Add the safe alignment call after this line
                lines.insert(i+1, '\n')
                lines.insert(i+2, '        # Safe array alignment to prevent broadcasting errors\n')
                lines.insert(i+3, '        y_pred_prediction, y_true_prediction = safe_array_alignment(\n')
                lines.insert(i+4, '            y_pred_prediction, y_true_prediction, "prediction evaluation"\n')
                lines.insert(i+5, '        )\n')
                changes_made += 1
                print(f"✅ Fixed line {i+1}: Removed flatten() from y_true_prediction and added safe alignment")
                break
        
        # Write the file back
        if changes_made > 0:
            with open('ml_core.py', 'w', encoding='utf-8') as f:
                f.writelines(lines)
            print(f"🎉 Successfully applied {changes_made} fixes!")
            return True
        else:
            print("⚠️ No changes were made - lines may have already been fixed")
            return False
            
    except Exception as e:
        print(f"❌ Error applying fix: {e}")
        return False

if __name__ == "__main__":
    success = apply_quick_fix()
    if success:
        print("\n✅ Broadcasting error fix applied!")
        print("   SAITS and BRITS models should now work without shape errors.")
        print("\n🧪 Test the fix by running your models again.")
    else:
        print("\n❌ Fix failed. Please check the file manually.")