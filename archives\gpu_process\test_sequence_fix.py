#!/usr/bin/env python3
"""
Test script to verify the sequence creation fix for prediction mode.
"""

import numpy as np
import pandas as pd
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.getcwd())

from data_handler import create_sequences
from enhanced_preprocessing import EnhancedLogPreprocessor

def create_test_data():
    """Create synthetic test data similar to well log data."""
    np.random.seed(42)
    
    wells = ['B-G-6', 'B-G-10', 'B-L-1']
    all_data = []
    
    for well in wells:
        n_points = 200
        
        # Create synthetic well log data
        md_values = np.linspace(1000, 1200, n_points)
        gr = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, n_points)) + np.random.normal(0, 5, n_points)
        rhob = 2.3 + 0.3 * np.cos(np.linspace(0, 3*np.pi, n_points)) + np.random.normal(0, 0.05, n_points)
        nphi = 0.15 + 0.1 * np.sin(np.linspace(0, 5*np.pi, n_points)) + np.random.normal(0, 0.02, n_points)
        
        # Target: P-WAVE with some realistic missing values
        pwave = 8000 + 2000 * (rhob - 2.3) / 0.3 - 5000 * nphi + np.random.normal(0, 200, n_points)
        
        # Add some missing values in P-WAVE (30% missing)
        missing_indices = np.random.choice(n_points, size=int(0.3 * n_points), replace=False)
        pwave[missing_indices] = np.nan
        
        well_data = pd.DataFrame({
            'WELL': well,
            'MD': md_values,
            'GR': gr,
            'RHOB': rhob,
            'NPHI': nphi,
            'P-WAVE': pwave
        })
        
        all_data.append(well_data)
    
    df = pd.concat(all_data, ignore_index=True)
    return df

def test_training_mode():
    """Test sequence creation in training mode (normal case)."""
    print("\n🧪 Testing TRAINING mode (normal case)...")
    
    df = create_test_data()
    feature_cols = ['GR', 'RHOB', 'NPHI', 'P-WAVE']
    
    print(f"Data shape: {df.shape}")
    print(f"P-WAVE missing: {df['P-WAVE'].isna().sum()} / {len(df)} ({df['P-WAVE'].isna().mean()*100:.1f}%)")
    
    # Test enhanced preprocessing
    sequences_enhanced, metadata_enhanced = create_sequences(
        df, 'WELL', feature_cols, sequence_len=32, use_enhanced=True
    )
    
    print(f"Enhanced sequences: {sequences_enhanced.shape}")
    print(f"Enhanced metadata: {len(metadata_enhanced)}")
    
    # Test standard preprocessing
    sequences_standard, metadata_standard = create_sequences(
        df, 'WELL', feature_cols, sequence_len=32, use_enhanced=False
    )
    
    print(f"Standard sequences: {sequences_standard.shape}")
    print(f"Standard metadata: {len(metadata_standard)}")
    
    return sequences_enhanced.shape[0] > 0 and sequences_standard.shape[0] > 0

def test_prediction_mode():
    """Test sequence creation in prediction mode (target column all NaN)."""
    print("\n🧪 Testing PREDICTION mode (target column all NaN)...")
    
    df = create_test_data()
    feature_cols = ['GR', 'RHOB', 'NPHI', 'P-WAVE']
    
    # Simulate prediction mode: forward-fill features, mask target entirely
    df_pred = df.copy()
    df_pred[['GR', 'RHOB', 'NPHI']] = df_pred[['GR', 'RHOB', 'NPHI']].ffill().bfill()
    df_pred['P-WAVE'] = np.nan  # Mask entire target column
    
    print(f"Prediction data shape: {df_pred.shape}")
    print(f"P-WAVE missing: {df_pred['P-WAVE'].isna().sum()} / {len(df_pred)} ({df_pred['P-WAVE'].isna().mean()*100:.1f}%)")
    print(f"GR missing: {df_pred['GR'].isna().sum()}")
    print(f"RHOB missing: {df_pred['RHOB'].isna().sum()}")
    print(f"NPHI missing: {df_pred['NPHI'].isna().sum()}")
    
    # Test enhanced preprocessing
    sequences_enhanced, metadata_enhanced = create_sequences(
        df_pred, 'WELL', feature_cols, sequence_len=32, use_enhanced=True
    )
    
    print(f"Enhanced sequences: {sequences_enhanced.shape}")
    print(f"Enhanced metadata: {len(metadata_enhanced)}")
    
    # Test standard preprocessing
    sequences_standard, metadata_standard = create_sequences(
        df_pred, 'WELL', feature_cols, sequence_len=32, use_enhanced=False
    )
    
    print(f"Standard sequences: {sequences_standard.shape}")
    print(f"Standard metadata: {len(metadata_standard)}")
    
    return sequences_enhanced.shape[0] > 0 and sequences_standard.shape[0] > 0

def main():
    """Run all tests."""
    print("🔧 Testing Sequence Creation Fix for Prediction Mode")
    print("=" * 60)
    
    try:
        # Test training mode
        training_success = test_training_mode()
        print(f"Training mode test: {'✅ PASSED' if training_success else '❌ FAILED'}")
        
        # Test prediction mode
        prediction_success = test_prediction_mode()
        print(f"Prediction mode test: {'✅ PASSED' if prediction_success else '❌ FAILED'}")
        
        # Overall result
        if training_success and prediction_success:
            print("\n🎉 ALL TESTS PASSED! The sequence creation fix is working correctly.")
            return True
        else:
            print("\n❌ SOME TESTS FAILED! The fix needs more work.")
            return False
            
    except Exception as e:
        print(f"\n💥 ERROR during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
