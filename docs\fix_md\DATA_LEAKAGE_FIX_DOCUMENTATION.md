# Data Leakage Fix Documentation

## Overview

This document describes the comprehensive fix applied to the SAITS/BRITS time series imputation implementation to eliminate data leakage issues that were causing unrealistic "perfect" performance metrics (R² = 1.0, MAE = 0).

## Problem Analysis

### 1. Primary Data Leakage Issue
**Location**: `ml_core.py` lines 1021-1025 (original)
**Problem**: During the prediction phase, the model was receiving sequences that contained the actual target values instead of NaN values, essentially giving the model the answers.

```python
# PROBLEMATIC CODE (FIXED)
df_scaled, full_scalers = normalize_data(df, all_features, ...)
sequences_to_predict, metadata = create_sequences(df_scaled, 'WELL', all_features, ...)
# df_scaled contained original target values, not NaNs
```

### 2. Validation Evaluation Issue
**Location**: `ml_core.py` lines 1005-1006 (original)
**Problem**: Evaluation was performed on all values, including those that were never missing, rather than only on artificially introduced missing values.

```python
# PROBLEMATIC CODE (FIXED)
y_pred_val = imputed_val_tensor[:, :, target_idx].flatten()
y_true_val = val_truth_tensor[:, :, target_idx].flatten()
# Evaluated on ALL values, not just artificially missing ones
```

### 3. Re-assembly Logic Issue
**Location**: `ml_core.py` lines 1057-1061 (original)
**Problem**: The re-assembly logic was replacing original data with "predicted" data that was actually just copied from the original, creating perfect correlations.

## Solution Implementation

### 1. Fixed Prediction Phase Data Preparation

**New Function**: `prepare_prediction_data()`
```python
def prepare_prediction_data(df_scaled, feature_cols, target_col):
    """
    Prepare data for prediction by filling feature columns while preserving target NaNs.
    This prevents data leakage by ensuring the model only sees features, not target values.
    """
    prediction_input_df = df_scaled.copy()
    
    # Forward-fill and back-fill feature columns to create continuous sequences
    prediction_input_df[feature_cols] = prediction_input_df[feature_cols].ffill().bfill()
    
    # Keep target column NaNs intact - this is what the model needs to predict
    return prediction_input_df
```

**Key Changes**:
- Feature columns are forward-filled and back-filled to ensure continuous sequences
- Target column NaNs are preserved so the model knows what to predict
- No future information leaks into past predictions

### 2. Fixed Validation Evaluation

**Before**:
```python
y_pred_val = imputed_val_tensor[:, :, target_idx].flatten()
y_true_val = val_truth_tensor[:, :, target_idx].flatten()
```

**After**:
```python
# Only evaluate on the initially missing values for a true test
val_mask = torch.isnan(val_train_tensor[:, :, target_idx])
y_pred_val = imputed_val_tensor[:, :, target_idx][val_mask]
y_true_val = val_truth_tensor[:, :, target_idx][val_mask]
```

**Key Changes**:
- Only evaluates performance on artificially introduced missing values
- Provides realistic performance metrics
- Prevents evaluation on values that were never missing

### 3. Fixed Re-assembly Logic

**Key Changes**:
- Removed problematic line that replaced original data with "predicted" data
- Proper inverse transformation of predictions
- Clear separation between `_pred` (model output) and `_imputed` (original + filled NaNs) columns

```python
# The '_pred' column shows the model's output for ALL points
res_df[pred_col] = imputed_df_scaled[target_col]

# The '_imputed' column shows the original data, with NaNs filled by the prediction
res_df[imp_col] = res_df[target_col].fillna(res_df[pred_col])
```

## Best Practices Implemented

### 1. Proper Data Masking
- **NaN Preservation**: Target column NaNs are preserved throughout the prediction pipeline
- **Feature Filling**: Feature columns are filled using forward-fill and back-fill to maintain temporal structure
- **No Future Leakage**: No future information influences past predictions

### 2. Realistic Evaluation
- **Artificial Missingness**: Evaluation only on artificially introduced missing values
- **Proper Metrics**: Performance metrics now reflect actual imputation capability
- **Validation Transparency**: Clear reporting of how many values were evaluated

### 3. Temporal Structure Preservation
- **Sequence Continuity**: Feature filling ensures sequences can be created without breaking temporal structure
- **Causal Relationships**: SAITS diagonal attention masks and BRITS bidirectional processing respect temporal boundaries

## Expected Performance Changes

### Before Fix
- R² ≈ 1.0 (unrealistic)
- MAE ≈ 0 (unrealistic)
- RMSE ≈ 0 (unrealistic)

### After Fix
- R² typically 0.5-0.8 (realistic for time series imputation)
- MAE > 0 (realistic error values)
- RMSE > 0 (realistic error values)

## Files Modified

1. **`ml_core.py`**:
   - Added `prepare_prediction_data()` helper function
   - Fixed `impute_logs_deep()` prediction phase
   - Fixed validation evaluation logic
   - Fixed re-assembly logic

## Verification Steps

To verify the fix is working:

1. **Check Prediction Input**: Verify that sequences fed to the model contain NaN values in the target column
2. **Check Evaluation**: Verify that validation metrics are calculated only on artificially missing values
3. **Check Performance**: Verify that performance metrics are realistic (R² < 1.0, MAE > 0)
4. **Check Columns**: Verify that `_pred` and `_imputed` columns are different and meaningful

## Technical Notes

- The fix maintains compatibility with both SAITS and BRITS models
- Enhanced preprocessing is still supported
- GPU acceleration remains functional
- All existing hyperparameter configurations are preserved

## Conclusion

This fix eliminates the data leakage issues that were causing unrealistic performance metrics in the SAITS/BRITS implementation. The models will now provide realistic performance metrics that accurately reflect their imputation capabilities on truly missing data.
