# Phase 3: Enhancement & Testing (Week 3)
## Advanced Deep Learning Models - Enhancement & Comprehensive Testing Phase

### 🎯 **Phase Objectives**
- Implement Enhanced UNet (MONAI-based true U-Net architecture) - **PRIORITY 3**
- Develop comprehensive testing suite for all advanced models
- Establish performance benchmarking framework
- Implement model comparison and ranking system
- Optimize hyperparameters and model configurations

### 📋 **Phase 3 Tasks Breakdown**

#### **Day 1-3: Enhanced UNet Implementation (Priority 3)**

##### Task 3.1: Enhanced UNet Core Implementation
```python
# models/advanced_models/enhanced_unet.py
"""
Enhanced UNet Implementation using MONAI
True U-Net architecture with skip connections for well log imputation
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from monai.networks.nets import UNet
from monai.networks.layers import Norm
from .base_model import BaseAdvancedModel
from typing import Dict, Any, Optional, List

class EnhancedUNet(BaseAdvancedModel):
    """
    Enhanced U-Net model using MONAI framework.
    True U-Net architecture with skip connections for spatial pattern recognition.
    """

    def __init__(self, n_features=4, sequence_len=64,
                 channels=(32, 64, 128, 256), strides=(2, 2, 2),
                 epochs=50, batch_size=32, learning_rate=1e-4, **kwargs):
        """
        Initialize Enhanced UNet model.

        Args:
            n_features: Number of log features
            sequence_len: Length of input sequences
            channels: Channel sizes for each level of U-Net
            strides: Stride sizes for downsampling
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, **kwargs)

        self.channels = channels
        self.strides = strides
        self.learning_rate = learning_rate

        # Validate parameters
        self._validate_parameters()

        # Training components
        self.optimizer = None
        self.criterion = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if len(self.channels) != len(self.strides) + 1:
            raise ValueError(f"channels length ({len(self.channels)}) must be strides length + 1 ({len(self.strides) + 1})")

        if self.sequence_len < 32:
            print("⚠️ Warning: sequence_len < 32 may not be optimal for U-Net architecture")

        # Check if sequence length is compatible with strides
        min_size = self.sequence_len
        for stride in self.strides:
            min_size = min_size // stride
        if min_size < 4:
            print(f"⚠️ Warning: sequence_len {self.sequence_len} may be too small for given strides {self.strides}")

    def _initialize_model(self) -> None:
        """Initialize the MONAI UNet model."""
        try:
            # Create 2D U-Net (treating sequence as spatial dimension)
            self.model = UNet(
                spatial_dims=2,
                in_channels=1,  # Single channel input (will reshape data)
                out_channels=1,  # Single channel output
                channels=self.channels,
                strides=self.strides,
                num_res_units=2,  # Residual units per level
                norm=Norm.BATCH,
                dropout=0.1,
                bias=True
            ).to(self.device)

            # Initialize optimizer and loss function
            self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
            self.criterion = nn.MSELoss()

            print(f"✅ Enhanced UNet initialized with channels {self.channels}")
            print(f"   Device: {self.device}")
            print(f"   Total parameters: {sum(p.numel() for p in self.model.parameters()):,}")

        except Exception as e:
            raise RuntimeError(f"Failed to initialize Enhanced UNet model: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Prepare data for U-Net training/prediction.

        Args:
            data: Input data tensor (batch, sequence, features)
            truth_data: Ground truth data (optional, for training)

        Returns:
            Dictionary with prepared tensors
        """
        # Reshape data for 2D U-Net: (batch, 1, sequence, features)
        if len(data.shape) == 3:
            data_reshaped = data.unsqueeze(1)  # Add channel dimension
        else:
            data_reshaped = data

        data_reshaped = data_reshaped.to(self.device)

        result = {'input': data_reshaped}

        if truth_data is not None:
            if len(truth_data.shape) == 3:
                truth_reshaped = truth_data.unsqueeze(1)
            else:
                truth_reshaped = truth_data
            truth_reshaped = truth_reshaped.to(self.device)
            result['target'] = truth_reshaped

        return result

    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor,
            epochs: Optional[int] = None, batch_size: Optional[int] = None) -> None:
        """
        Train the Enhanced UNet model.

        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
        """
        if self.model is None:
            self._initialize_model()

        epochs = epochs or self.epochs
        print(f"Training Enhanced UNet for {epochs} epochs...")

        # Prepare data
        data_dict = self._prepare_data(train_data, truth_data)
        input_data = data_dict['input']
        target_data = data_dict['target']

        # Create missing value mask
        missing_mask = torch.isnan(input_data)
        input_data_filled = input_data.clone()
        input_data_filled[missing_mask] = 0.0  # Fill NaN with zeros for U-Net

        self.model.train()
        training_losses = []

        for epoch in range(epochs):
            self.optimizer.zero_grad()

            # Forward pass
            outputs = self.model(input_data_filled)

            # Calculate loss only on non-missing values
            valid_mask = ~missing_mask
            if valid_mask.sum() > 0:
                loss = self.criterion(outputs[valid_mask], target_data[valid_mask])
            else:
                loss = self.criterion(outputs, target_data)

            # Backward pass
            loss.backward()
            self.optimizer.step()

            training_losses.append(loss.item())

            if (epoch + 1) % 10 == 0:
                print(f"   Epoch {epoch + 1}/{epochs}, Loss: {loss.item():.6f}")

        self.is_fitted = True
        self.training_history = {'losses': training_losses}
        print("Enhanced UNet training completed!")

    def predict(self, data: torch.Tensor) -> torch.Tensor:
        """
        Predict/impute missing values using Enhanced UNet.

        Args:
            data: Input data with missing values

        Returns:
            Imputed data tensor
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")

        self.model.eval()

        with torch.no_grad():
            # Prepare data
            data_dict = self._prepare_data(data)
            input_data = data_dict['input']

            # Handle missing values
            missing_mask = torch.isnan(input_data)
            input_data_filled = input_data.clone()
            input_data_filled[missing_mask] = 0.0

            # Predict
            outputs = self.model(input_data_filled)

            # Combine original and predicted values
            result = input_data.clone()
            result[missing_mask] = outputs[missing_mask]

            # Remove channel dimension and return to CPU
            if result.shape[1] == 1:
                result = result.squeeze(1)

            return result.cpu()

    def get_model_complexity(self) -> Dict[str, int]:
        """Get model complexity metrics."""
        if self.model is None:
            self._initialize_model()

        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)

        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'channels': self.channels,
            'strides': self.strides,
            'complexity_score': 2  # Medium-high complexity
        }

    def get_feature_maps(self, data: torch.Tensor, layer_name: str = 'encoder') -> Optional[torch.Tensor]:
        """
        Extract feature maps from specified layer.

        Args:
            data: Input data tensor
            layer_name: Name of layer to extract features from

        Returns:
            Feature maps tensor or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting feature maps")
            return None

        # This would require hooks to extract intermediate features
        # For now, return None and implement in future versions
        print("ℹ️ Feature map extraction not yet implemented")
        return None
```

##### Task 3.2: Enhanced UNet Testing & Validation
```python
# tests/test_enhanced_unet.py
import unittest
import torch
import numpy as np

class TestEnhancedUNet(unittest.TestCase):
    """Test suite for Enhanced UNet implementation."""

    def setUp(self):
        """Set up test data."""
        self.n_features = 4
        self.sequence_len = 64
        self.batch_size = 8

        # Create test data
        self.test_data = torch.randn(self.batch_size, self.sequence_len, self.n_features)
        self.test_data = torch.abs(self.test_data)  # Well logs are typically positive
        self.test_data[self.test_data > 2.0] = float('nan')  # Add missing values
        self.truth_data = torch.abs(torch.randn(self.batch_size, self.sequence_len, self.n_features))

    def test_model_initialization(self):
        """Test Enhanced UNet model initialization."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32, 64),  # Small for testing
            strides=(2, 2),
            epochs=2,
            batch_size=self.batch_size
        )

        self.assertEqual(model.n_features, self.n_features)
        self.assertEqual(model.sequence_len, self.sequence_len)
        self.assertFalse(model.is_fitted)

    def test_parameter_validation(self):
        """Test parameter validation."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        # Test invalid channels/strides combination
        with self.assertRaises(ValueError):
            EnhancedUNet(channels=(32, 64), strides=(2, 2, 2))  # Mismatched lengths

    def test_training_and_prediction(self):
        """Test model training and prediction."""
        try:
            from models.advanced_models.enhanced_unet import EnhancedUNet
        except ImportError:
            self.skipTest("Enhanced UNet not available - MONAI not installed")

        model = EnhancedUNet(
            n_features=self.n_features,
            sequence_len=self.sequence_len,
            channels=(16, 32),  # Small for testing
            strides=(2,),
            epochs=2,
            batch_size=self.batch_size
        )

        # Test training
        model.fit(self.test_data, self.truth_data, epochs=2)
        self.assertTrue(model.is_fitted)

        # Test prediction
        predictions = model.predict(self.test_data)
        self.assertEqual(predictions.shape, self.test_data.shape)
        self.assertFalse(torch.isnan(predictions).all())

if __name__ == '__main__':
    unittest.main()
```

#### **Day 4-5: Comprehensive Testing Suite Development**

##### Task 3.3: Performance Benchmarking Framework
```python
# tests/performance_benchmarking.py
"""
Comprehensive performance benchmarking framework for all models
"""

import time
import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns

class ModelBenchmark:
    """
    Comprehensive benchmarking suite for well log imputation models.
    """

    def __init__(self, test_data_sizes: List[Tuple[int, int, int]] = None):
        """
        Initialize benchmark suite.

        Args:
            test_data_sizes: List of (batch_size, sequence_len, n_features) tuples for testing
        """
        self.test_data_sizes = test_data_sizes or [
            (16, 32, 4),   # Small
            (32, 64, 4),   # Medium
            (64, 128, 4),  # Large
        ]
        self.results = {}

    def generate_test_data(self, batch_size: int, sequence_len: int, n_features: int,
                          missing_rate: float = 0.3) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Generate realistic test data for benchmarking.

        Args:
            batch_size: Number of sequences
            sequence_len: Length of each sequence
            n_features: Number of features
            missing_rate: Proportion of values to make missing

        Returns:
            Tuple of (data_with_missing, ground_truth)
        """
        # Generate realistic well log data
        np.random.seed(42)  # For reproducibility

        # Create correlated features (typical of well logs)
        base_trend = np.linspace(0, 10, sequence_len)
        noise = np.random.normal(0, 0.5, (batch_size, sequence_len, n_features))

        ground_truth = np.zeros((batch_size, sequence_len, n_features))
        for i in range(batch_size):
            for j in range(n_features):
                # Add feature-specific patterns
                feature_trend = base_trend + j * 2 + np.random.normal(0, 1)
                ground_truth[i, :, j] = feature_trend + noise[i, :, j]

        # Ensure positive values (typical for well logs)
        ground_truth = np.abs(ground_truth) + 0.1

        # Create missing data
        data_with_missing = ground_truth.copy()
        missing_mask = np.random.random(ground_truth.shape) < missing_rate
        data_with_missing[missing_mask] = np.nan

        return torch.from_numpy(data_with_missing).float(), torch.from_numpy(ground_truth).float()

    def benchmark_model(self, model_class, model_params: Dict[str, Any],
                       model_name: str) -> Dict[str, Any]:
        """
        Benchmark a single model across different data sizes.

        Args:
            model_class: Model class to benchmark
            model_params: Parameters for model initialization
            model_name: Name for reporting

        Returns:
            Dictionary with benchmark results
        """
        print(f"🔍 Benchmarking {model_name}...")

        model_results = {
            'model_name': model_name,
            'size_results': [],
            'average_metrics': {},
            'complexity': {}
        }

        for batch_size, sequence_len, n_features in self.test_data_sizes:
            print(f"   Testing size: {batch_size}x{sequence_len}x{n_features}")

            # Generate test data
            test_data, truth_data = self.generate_test_data(batch_size, sequence_len, n_features)

            # Update model parameters
            current_params = model_params.copy()
            current_params.update({
                'n_features': n_features,
                'sequence_len': sequence_len,
                'batch_size': min(batch_size, 16),  # Limit batch size for memory
                'epochs': 5  # Quick training for benchmarking
            })

            try:
                # Initialize model
                model = model_class(**current_params)

                # Measure training time
                start_time = time.time()
                model.fit(test_data, truth_data, epochs=current_params['epochs'])
                training_time = time.time() - start_time

                # Measure prediction time
                start_time = time.time()
                predictions = model.predict(test_data)
                prediction_time = time.time() - start_time

                # Calculate metrics
                metrics = self._calculate_metrics(truth_data, predictions, test_data)

                # Get model complexity
                complexity = model.get_model_complexity() if hasattr(model, 'get_model_complexity') else {}

                size_result = {
                    'data_size': (batch_size, sequence_len, n_features),
                    'training_time': training_time,
                    'prediction_time': prediction_time,
                    'metrics': metrics,
                    'complexity': complexity
                }

                model_results['size_results'].append(size_result)
                print(f"      MAE: {metrics['mae']:.4f}, R²: {metrics['r2']:.4f}, Time: {training_time:.2f}s")

            except Exception as e:
                print(f"      ❌ Failed: {e}")
                size_result = {
                    'data_size': (batch_size, sequence_len, n_features),
                    'error': str(e)
                }
                model_results['size_results'].append(size_result)

        # Calculate average metrics
        valid_results = [r for r in model_results['size_results'] if 'metrics' in r]
        if valid_results:
            avg_metrics = {}
            for metric in ['mae', 'rmse', 'r2']:
                values = [r['metrics'][metric] for r in valid_results if metric in r['metrics']]
                if values:
                    avg_metrics[metric] = np.mean(values)
            model_results['average_metrics'] = avg_metrics

        return model_results

    def _calculate_metrics(self, truth_data: torch.Tensor, predictions: torch.Tensor,
                          original_data: torch.Tensor) -> Dict[str, float]:
        """Calculate comprehensive metrics for model evaluation."""
        # Convert to numpy
        truth_np = truth_data.cpu().numpy()
        pred_np = predictions.cpu().numpy()
        orig_np = original_data.cpu().numpy()

        # Create mask for originally missing values
        missing_mask = np.isnan(orig_np)

        # Flatten arrays
        truth_flat = truth_np.flatten()
        pred_flat = pred_np.flatten()
        missing_flat = missing_mask.flatten()

        # Calculate metrics on all valid values
        valid_mask = ~np.isnan(truth_flat) & ~np.isnan(pred_flat)

        if valid_mask.sum() == 0:
            return {'mae': float('inf'), 'rmse': float('inf'), 'r2': -float('inf')}

        y_true = truth_flat[valid_mask]
        y_pred = pred_flat[valid_mask]

        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)

        # Additional metrics
        mape = np.mean(np.abs((y_true - y_pred) / (y_true + 1e-8))) * 100
        bias = np.mean(y_pred - y_true)

        return {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'mape': mape,
            'bias': bias,
            'n_samples': len(y_true)
        }

    def run_comprehensive_benchmark(self, models_to_test: Dict[str, Tuple]) -> Dict[str, Any]:
        """
        Run comprehensive benchmark across all specified models.

        Args:
            models_to_test: Dict of {model_name: (model_class, model_params)}

        Returns:
            Complete benchmark results
        """
        print("🚀 Starting comprehensive model benchmark...")

        all_results = {}

        for model_name, (model_class, model_params) in models_to_test.items():
            try:
                result = self.benchmark_model(model_class, model_params, model_name)
                all_results[model_name] = result
            except Exception as e:
                print(f"❌ Failed to benchmark {model_name}: {e}")
                all_results[model_name] = {'error': str(e)}

        # Generate comparison report
        self.results = all_results
        comparison_report = self._generate_comparison_report(all_results)

        return {
            'individual_results': all_results,
            'comparison_report': comparison_report
        }

    def _generate_comparison_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive comparison report."""
        valid_results = {k: v for k, v in results.items() if 'average_metrics' in v}

        if not valid_results:
            return {'error': 'No valid results to compare'}

        # Create comparison DataFrame
        comparison_data = []
        for model_name, result in valid_results.items():
            metrics = result['average_metrics']
            row = {'model': model_name}
            row.update(metrics)
            comparison_data.append(row)

        df = pd.DataFrame(comparison_data)

        # Rankings
        rankings = {}
        if 'mae' in df.columns:
            rankings['mae'] = df.nsmallest(len(df), 'mae')['model'].tolist()
        if 'r2' in df.columns:
            rankings['r2'] = df.nlargest(len(df), 'r2')['model'].tolist()
        if 'rmse' in df.columns:
            rankings['rmse'] = df.nsmallest(len(df), 'rmse')['model'].tolist()

        # Overall ranking (composite score)
        if 'mae' in df.columns and 'r2' in df.columns:
            df['composite_score'] = (
                (1 - df['r2'].fillna(0)) * 0.4 +  # R² penalty (lower is better)
                (df['mae'] / df['mae'].max()) * 0.4 +  # Normalized MAE
                (df['rmse'] / df['rmse'].max()) * 0.2   # Normalized RMSE
            )
            rankings['overall'] = df.nsmallest(len(df), 'composite_score')['model'].tolist()

        return {
            'comparison_table': df.to_dict('records'),
            'rankings': rankings,
            'best_models': {
                'accuracy': rankings.get('r2', [None])[0],
                'precision': rankings.get('mae', [None])[0],
                'overall': rankings.get('overall', [None])[0]
            }
        }

    def create_benchmark_visualizations(self, save_path: str = None):
        """Create comprehensive benchmark visualization plots."""
        if not self.results:
            print("❌ No benchmark results available. Run benchmark first.")
            return

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Model Performance Benchmark Results', fontsize=16, fontweight='bold')

        # Prepare data for plotting
        plot_data = []
        for model_name, result in self.results.items():
            if 'size_results' in result:
                for size_result in result['size_results']:
                    if 'metrics' in size_result:
                        row = {
                            'model': model_name,
                            'data_size': f"{size_result['data_size'][0]}x{size_result['data_size'][1]}",
                            **size_result['metrics'],
                            'training_time': size_result['training_time']
                        }
                        plot_data.append(row)

        if not plot_data:
            print("❌ No valid data for visualization")
            return

        df_plot = pd.DataFrame(plot_data)

        # Plot 1: MAE comparison
        if 'mae' in df_plot.columns:
            sns.barplot(data=df_plot, x='model', y='mae', ax=axes[0, 0])
            axes[0, 0].set_title('Mean Absolute Error (Lower is Better)')
            axes[0, 0].tick_params(axis='x', rotation=45)

        # Plot 2: R² comparison
        if 'r2' in df_plot.columns:
            sns.barplot(data=df_plot, x='model', y='r2', ax=axes[0, 1])
            axes[0, 1].set_title('R² Score (Higher is Better)')
            axes[0, 1].tick_params(axis='x', rotation=45)

        # Plot 3: Training time comparison
        if 'training_time' in df_plot.columns:
            sns.barplot(data=df_plot, x='model', y='training_time', ax=axes[0, 2])
            axes[0, 2].set_title('Training Time (Lower is Better)')
            axes[0, 2].tick_params(axis='x', rotation=45)

        # Plot 4: Performance vs Data Size
        if 'mae' in df_plot.columns:
            sns.lineplot(data=df_plot, x='data_size', y='mae', hue='model', ax=axes[1, 0])
            axes[1, 0].set_title('MAE vs Data Size')
            axes[1, 0].tick_params(axis='x', rotation=45)

        # Plot 5: Accuracy vs Speed Trade-off
        if 'r2' in df_plot.columns and 'training_time' in df_plot.columns:
            avg_by_model = df_plot.groupby('model').agg({'r2': 'mean', 'training_time': 'mean'}).reset_index()
            sns.scatterplot(data=avg_by_model, x='training_time', y='r2', ax=axes[1, 1], s=100)
            for i, row in avg_by_model.iterrows():
                axes[1, 1].annotate(row['model'], (row['training_time'], row['r2']),
                                   xytext=(5, 5), textcoords='offset points')
            axes[1, 1].set_title('Accuracy vs Training Speed')

        # Plot 6: Model Ranking Summary
        if hasattr(self, 'results') and self.results:
            comparison_report = self._generate_comparison_report(self.results)
            if 'rankings' in comparison_report and 'overall' in comparison_report['rankings']:
                rankings = comparison_report['rankings']['overall']
                y_pos = np.arange(len(rankings))
                axes[1, 2].barh(y_pos, range(len(rankings), 0, -1))
                axes[1, 2].set_yticks(y_pos)
                axes[1, 2].set_yticklabels(rankings)
                axes[1, 2].set_xlabel('Rank (Higher is Better)')
                axes[1, 2].set_title('Overall Model Ranking')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 Benchmark visualization saved to {save_path}")

        plt.show()
```