#!/usr/bin/env python3
"""
Test script to verify the R² discrepancy fix is working correctly.
This script tests the enhanced evaluation system for deep learning models.
"""

import numpy as np
import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """Create synthetic well log data for testing."""
    np.random.seed(42)
    
    # Create synthetic data for 3 wells
    wells = ['WELL_A', 'WELL_B', 'WELL_C']
    data = []
    
    for well in wells:
        n_points = 200
        depth = np.linspace(1000, 1500, n_points)
        
        # Create correlated features
        gamma_ray = 50 + 30 * np.sin(depth / 100) + np.random.normal(0, 5, n_points)
        resistivity = 10 + 5 * np.cos(depth / 80) + np.random.normal(0, 2, n_points)
        porosity = 0.2 + 0.1 * np.sin(depth / 120) + np.random.normal(0, 0.02, n_points)
        
        # Target log with some relationship to features
        permeability = (porosity * 100) + (gamma_ray * 0.1) - (resistivity * 0.5) + np.random.normal(0, 5, n_points)
        
        for i in range(n_points):
            data.append({
                'WELL': well,
                'MD': depth[i],
                'GR': gamma_ray[i],
                'RT': resistivity[i],
                'PHIE': porosity[i],
                'PERM': permeability[i]
            })
    
    df = pd.DataFrame(data)
    
    # Introduce some missing values in target
    missing_mask = np.random.random(len(df)) < 0.1
    df.loc[missing_mask, 'PERM'] = np.nan
    
    return df

def test_evaluation_fix():
    """Test the enhanced evaluation system."""
    print("🧪 Testing R² Discrepancy Fix")
    print("=" * 50)
    
    # Create test data
    print("📊 Creating synthetic test data...")
    df = create_test_data()
    print(f"   • Created data for {df['WELL'].nunique()} wells")
    print(f"   • Total points: {len(df)}")
    print(f"   • Missing values in target: {df['PERM'].isna().sum()}")
    
    # Test parameters
    feature_cols = ['GR', 'RT', 'PHIE']
    target_col = 'PERM'
    
    # Test model configuration (using a simple autoencoder for testing)
    model_config = {
        'name': 'Autoencoder',
        'class': 'BasicAutoencoder',
        'type': 'deep'
    }
    
    hparams = {
        'sequence_len': 32,
        'hidden_dim': 64,
        'num_layers': 2,
        'dropout': 0.1,
        'learning_rate': 0.001,
        'batch_size': 32,
        'epochs': 10
    }
    
    try:
        # Import the enhanced function
        from ml_core import impute_logs_deep
        
        print("\n🔧 Testing enhanced impute_logs_deep function...")
        
        # Run the enhanced evaluation
        result_df, results = impute_logs_deep(
            df=df,
            feature_cols=feature_cols,
            target_col=target_col,
            model_config=model_config,
            hparams=hparams,
            use_enhanced_preprocessing=True
        )
        
        print("\n📊 Results Analysis:")
        print("-" * 30)
        
        # Check if comprehensive evaluation is present
        if 'comprehensive_evaluation' in results:
            comp_eval = results['comprehensive_evaluation']
            print("✅ Comprehensive evaluation found!")
            
            if 'imputation_metrics' in comp_eval:
                imp = comp_eval['imputation_metrics']
                print(f"   Imputation R²: {imp.get('r2', 'N/A'):.4f}")
                print(f"   Imputation MAE: {imp.get('mae', 'N/A'):.4f}")
            
            if 'prediction_metrics' in comp_eval:
                pred = comp_eval['prediction_metrics']
                print(f"   Prediction R²: {pred.get('r2', 'N/A'):.4f}")
                print(f"   Prediction MAE: {pred.get('mae', 'N/A'):.4f}")
            
            if 'combined_metrics' in comp_eval:
                comb = comp_eval['combined_metrics']
                print(f"   R² Discrepancy: {comb.get('r2_discrepancy', 'N/A'):.4f}")
        
        # Check individual metrics
        if 'imputation_metrics' in results:
            print("✅ Imputation metrics found in results!")
        
        if 'prediction_metrics' in results:
            print("✅ Prediction metrics found in results!")
        
        # Check evaluation results
        if 'evaluations' in results and len(results['evaluations']) > 0:
            eval_result = results['evaluations'][0]
            if 'comprehensive_evaluation' in eval_result:
                print("✅ Comprehensive evaluation found in evaluation results!")
        
        print("\n🎉 Test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all required modules are available.")
        return False
    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evaluation_functions():
    """Test the individual evaluation functions."""
    print("\n🧪 Testing Individual Evaluation Functions")
    print("=" * 50)
    
    try:
        from ml_core import evaluate_imputation_and_prediction, create_enhanced_evaluation_report
        print("✅ Successfully imported evaluation functions!")
        
        # Test create_enhanced_evaluation_report
        mock_results = {
            'Model_A': {
                'imputation_metrics': {'mae': 0.1, 'r2': 0.95, 'rmse': 0.15, 'n_samples': 1000},
                'prediction_metrics': {'mae': 0.3, 'r2': 0.75, 'rmse': 0.4, 'n_samples': 1000},
                'combined_metrics': {'r2_discrepancy': 0.2, 'performance_ratio': 0.79, 'task_complexity_increase': 3.0}
            },
            'Model_B': {
                'imputation_metrics': {'mae': 0.2, 'r2': 0.85, 'rmse': 0.25, 'n_samples': 1000},
                'prediction_metrics': {'mae': 0.25, 'r2': 0.80, 'rmse': 0.3, 'n_samples': 1000},
                'combined_metrics': {'r2_discrepancy': 0.05, 'performance_ratio': 0.94, 'task_complexity_increase': 1.25}
            }
        }
        
        report = create_enhanced_evaluation_report(mock_results, "test_evaluation_report.txt")
        print("✅ Successfully created evaluation report!")
        print("   Report saved to: test_evaluation_report.txt")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting R² Discrepancy Fix Tests")
    print("=" * 60)
    
    # Test 1: Individual functions
    test1_passed = test_evaluation_functions()
    
    # Test 2: Full integration (only if basic models are available)
    print("\n" + "=" * 60)
    test2_passed = test_evaluation_fix()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   • Evaluation Functions: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   • Full Integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The R² discrepancy fix is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please check the error messages above.")