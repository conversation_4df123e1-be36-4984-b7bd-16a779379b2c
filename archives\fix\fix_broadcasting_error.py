#!/usr/bin/env python3
"""
Broadcasting Error Fix for SAITS and BRITS Models
This script applies the necessary fixes to prevent the broadcasting error.
"""

import re

def apply_broadcasting_fix():
    """Apply the broadcasting error fix to ml_core.py"""
    
    print("🔧 Applying Broadcasting Error Fix...")
    
    # Read the current file
    with open('ml_core.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix 1: Add safe array alignment after imputation extraction
    imputation_pattern = r'(y_true_imputation = val_sequences_true\[:, :, target_idx\]\[val_mask\]\.detach\(\)\.cpu\(\)\.numpy\(\)\n\n        # Remove any remaining NaNs)'
    imputation_replacement = r'\1\n        # Safe array alignment to prevent broadcasting errors\n        y_pred_imputation, y_true_imputation = safe_array_alignment(\n            y_pred_imputation, y_true_imputation, "imputation evaluation"\n        )\n        \n        if len(y_pred_imputation) == 0 or len(y_true_imputation) == 0:\n            print("⚠️ No valid imputation samples after alignment")\n        else:'
    
    # Fix 2: Remove .flatten() and add safe alignment in prediction section
    prediction_pattern = r'(# Extract predictions and ground truth for all positions\n        y_pred_prediction = predicted_sequences\[:, :, target_idx\]\.detach\(\)\.cpu\(\)\.numpy\(\)\.flatten\(\)\n        y_true_prediction = test_true_tensor\[:, :, target_idx\]\.detach\(\)\.cpu\(\)\.numpy\(\)\.flatten\(\))'
    prediction_replacement = r'# Extract predictions and ground truth for all positions\n        y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy()\n        y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy()\n\n        # Safe array alignment to prevent broadcasting errors\n        y_pred_prediction, y_true_prediction = safe_array_alignment(\n            y_pred_prediction, y_true_prediction, "prediction evaluation"\n        )'
    
    # Fix 3: Add length check for prediction evaluation
    prediction_nan_pattern = r'(# Remove NaNs\n        valid_idx = ~np\.isnan\(y_true_prediction\) & ~np\.isnan\(y_pred_prediction\))'
    prediction_nan_replacement = r'# Remove NaNs\n        if len(y_pred_prediction) == 0 or len(y_true_prediction) == 0:\n            print("⚠️ No valid prediction samples after alignment")\n        else:\n            valid_idx = ~np.isnan(y_true_prediction) & ~np.isnan(y_pred_prediction)'
    
    # Apply fixes
    fixes_applied = 0
    
    if re.search(imputation_pattern, content):
        content = re.sub(imputation_pattern, imputation_replacement, content)
        fixes_applied += 1
        print("✅ Applied imputation evaluation fix")
    
    if re.search(prediction_pattern, content):
        content = re.sub(prediction_pattern, prediction_replacement, content)
        fixes_applied += 1
        print("✅ Applied prediction evaluation fix")
    
    if re.search(prediction_nan_pattern, content):
        content = re.sub(prediction_nan_pattern, prediction_nan_replacement, content)
        fixes_applied += 1
        print("✅ Applied prediction NaN handling fix")
    
    # Write the fixed content back
    if fixes_applied > 0:
        with open('ml_core.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"🎉 Successfully applied {fixes_applied} fixes to ml_core.py")
        return True
    else:
        print("⚠️ No fixes were applied - patterns may have changed")
        return False

if __name__ == "__main__":
    success = apply_broadcasting_fix()
    if success:
        print("\n✅ Broadcasting error fix applied successfully!")
        print("   The SAITS and BRITS models should now work without shape errors.")
    else:
        print("\n❌ Fix application failed. Manual intervention may be required.")