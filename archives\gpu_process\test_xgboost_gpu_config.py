#!/usr/bin/env python3
"""
Test script for XGBoost GPU configuration and fallback mechanisms.
Validates that XGBoost works reliably on systems with or without CUDA support.
"""

import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, Any
import warnings

# Add the current directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_xgboost_gpu_utilities():
    """Test the XGBoost GPU utilities."""
    print("🧪 Testing XGBoost GPU Utilities")
    print("="*50)
    
    try:
        from utils.xgboost_gpu_utils import (
            get_xgboost_gpu_manager, 
            create_optimized_xgboost_model,
            check_xgboost_gpu_support
        )
        from xgboost import XGBRegressor
        
        # Test GPU manager initialization
        manager = get_xgboost_gpu_manager()
        manager.print_configuration_report()
        
        # Test GPU support check
        gpu_support = check_xgboost_gpu_support()
        print(f"\n🔍 GPU Support Available: {'✅' if gpu_support else '❌'}")
        
        # Test model creation with various configurations
        test_configs = [
            {'n_estimators': 10, 'max_depth': 3},
            {'n_estimators': 50, 'learning_rate': 0.1},
            {'n_estimators': 100, 'tree_method': 'hist'}  # This should be handled properly
        ]
        
        for i, config in enumerate(test_configs, 1):
            print(f"\n📦 Test Configuration {i}: {config}")
            try:
                model = create_optimized_xgboost_model(XGBRegressor, config)
                print(f"   ✅ Model created successfully")
                
                # Test with sample data
                X_test = np.random.random((100, 5))
                y_test = np.random.random(100)
                
                model.fit(X_test, y_test)
                predictions = model.predict(X_test)
                print(f"   ✅ Model training and prediction successful")
                print(f"   📊 Prediction shape: {predictions.shape}")
                
            except Exception as e:
                print(f"   ❌ Configuration {i} failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ XGBoost GPU utilities test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ml_core_integration():
    """Test XGBoost integration with ml_core."""
    print("\n🧪 Testing ML Core Integration")
    print("="*50)
    
    try:
        from ml_core import create_gpu_optimized_model, MODEL_REGISTRY
        
        # Test XGBoost model creation through ml_core
        xgb_config = MODEL_REGISTRY['xgboost']
        print(f"📋 XGBoost config: {xgb_config['name']}")
        print(f"   Modern GPU config: {xgb_config.get('use_modern_gpu_config', False)}")
        
        # Test hyperparameters
        test_hyperparams = {
            'n_estimators': 50,
            'learning_rate': 0.1,
            'max_depth': 4
        }
        
        print(f"\n🔧 Creating XGBoost model with hyperparams: {test_hyperparams}")
        
        model = create_gpu_optimized_model('xgboost', test_hyperparams)
        
        if model is not None:
            print("✅ XGBoost model created successfully through ml_core")
            
            # Test the model with sample data
            X_test = np.random.random((100, 5))
            y_test = np.random.random(100)
            
            model.fit(X_test, y_test)
            predictions = model.predict(X_test)
            print(f"✅ Model training and prediction successful")
            print(f"📊 Model parameters: {model.get_params()}")
            
            # Check for modern parameters
            params = model.get_params()
            if 'device' in params:
                print(f"✅ Modern 'device' parameter found: {params['device']}")
            if 'tree_method' in params:
                print(f"✅ Tree method: {params['tree_method']}")
            
            # Check for deprecated parameters (should not be present)
            deprecated_params = ['gpu_id', 'predictor']
            for param in deprecated_params:
                if param in params:
                    print(f"⚠️ Deprecated parameter '{param}' still present: {params[param]}")
                else:
                    print(f"✅ Deprecated parameter '{param}' properly removed")
            
            return True
        else:
            print("❌ Model creation returned None")
            return False
            
    except Exception as e:
        print(f"❌ ML Core integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_scenarios():
    """Test various fallback scenarios."""
    print("\n🧪 Testing Fallback Scenarios")
    print("="*50)
    
    try:
        from utils.xgboost_gpu_utils import XGBoostGPUManager
        from xgboost import XGBRegressor
        
        manager = XGBoostGPUManager()
        
        # Test scenario 1: Force CPU configuration
        print("📦 Scenario 1: Force CPU configuration")
        cpu_params = {'n_estimators': 10, 'device': 'cpu', 'tree_method': 'hist'}
        optimized_params, is_gpu = manager.get_optimal_config(cpu_params)
        print(f"   Optimized params: {optimized_params}")
        print(f"   GPU enabled: {is_gpu}")
        
        model = XGBRegressor(**optimized_params)
        X_test = np.random.random((50, 3))
        y_test = np.random.random(50)
        model.fit(X_test, y_test)
        print("   ✅ CPU configuration works")
        
        # Test scenario 2: Remove deprecated parameters
        print("\n📦 Scenario 2: Handle deprecated parameters")
        deprecated_params = {
            'n_estimators': 10,
            'gpu_id': 0,  # Deprecated
            'predictor': 'gpu_predictor',  # Deprecated
            'tree_method': 'gpu_hist'  # Old style
        }
        
        clean_params, is_gpu = manager.get_optimal_config(deprecated_params)
        print(f"   Cleaned params: {clean_params}")
        print(f"   GPU enabled: {is_gpu}")
        
        # Verify deprecated parameters are removed
        deprecated_found = any(param in clean_params for param in ['gpu_id', 'predictor'])
        if not deprecated_found:
            print("   ✅ Deprecated parameters properly removed")
        else:
            print("   ❌ Some deprecated parameters still present")
            return False
        
        # Test scenario 3: Safe model creation with error handling
        print("\n📦 Scenario 3: Safe model creation")
        try:
            safe_model = manager.create_safe_model(
                XGBRegressor, 
                {'n_estimators': 10, 'max_depth': 3}
            )
            print("   ✅ Safe model creation successful")
        except Exception as e:
            print(f"   ❌ Safe model creation failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Fallback scenarios test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_conditions():
    """Test various error conditions and edge cases."""
    print("\n🧪 Testing Error Conditions")
    print("="*50)
    
    try:
        from utils.xgboost_gpu_utils import XGBoostGPUManager
        from xgboost import XGBRegressor
        
        manager = XGBoostGPUManager()
        
        # Test with invalid parameters
        print("📦 Testing invalid parameter handling")
        invalid_params = {
            'n_estimators': 10,
            'invalid_param': 'invalid_value',
            'another_invalid': 123
        }
        
        try:
            # This should handle invalid parameters gracefully
            clean_params, _ = manager.get_optimal_config(invalid_params)
            print(f"   Cleaned params: {clean_params}")
            print("   ✅ Invalid parameters handled gracefully")
        except Exception as e:
            print(f"   ⚠️ Invalid parameter handling: {e}")
        
        # Test with empty parameters
        print("\n📦 Testing empty parameter handling")
        empty_params = {}
        clean_params, _ = manager.get_optimal_config(empty_params)
        print(f"   Result with empty params: {clean_params}")
        
        # Test model creation with minimal parameters
        print("\n📦 Testing minimal model creation")
        minimal_model = manager.create_safe_model(XGBRegressor, {'n_estimators': 5})
        print("   ✅ Minimal model creation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error conditions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_data():
    """Create test data for model validation."""
    np.random.seed(42)
    
    # Create synthetic well log data
    n_samples = 1000
    n_features = 5
    
    X = np.random.random((n_samples, n_features))
    # Add some realistic patterns
    X[:, 0] = X[:, 0] * 100 + 50  # GR (50-150)
    X[:, 1] = X[:, 1] * 0.3 + 0.1  # NPHI (0.1-0.4)
    X[:, 2] = X[:, 2] * 0.5 + 2.0  # RHOB (2.0-2.5)
    X[:, 3] = X[:, 3] * 2.0 + 2.0  # PE (2.0-4.0)
    X[:, 4] = X[:, 4] * 0.2 + 0.05  # Target (0.05-0.25)
    
    # Create target with some correlation to features
    y = (X[:, 1] * 0.5 + X[:, 2] * 0.3 + np.random.normal(0, 0.02, n_samples))
    
    return X, y

def main():
    """Run all XGBoost configuration tests."""
    print("🚀 XGBOOST GPU CONFIGURATION TEST SUITE")
    print("="*60)
    print("Testing XGBoost GPU configuration and fallback mechanisms")
    print("="*60)
    
    # Suppress XGBoost warnings for cleaner output
    warnings.filterwarnings('ignore', category=UserWarning, module='xgboost')
    
    tests = [
        ("XGBoost GPU Utilities", test_xgboost_gpu_utilities),
        ("ML Core Integration", test_ml_core_integration),
        ("Fallback Scenarios", test_fallback_scenarios),
        ("Error Conditions", test_error_conditions),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! XGBoost GPU configuration is working correctly.")
        print("💡 Your system can now handle XGBoost with proper GPU/CPU fallback.")
    else:
        print("⚠️ Some tests failed. Review the output above for details.")
    
    # Print final configuration status
    try:
        from utils.xgboost_gpu_utils import get_xgboost_gpu_manager
        manager = get_xgboost_gpu_manager()
        print("\n📋 Final XGBoost Configuration Status:")
        info = manager.get_version_info()
        if info['cuda_support'] and info['gpu_available']:
            print("   🚀 GPU acceleration available and working")
        else:
            print("   💻 CPU mode configured and working")
    except:
        pass
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
