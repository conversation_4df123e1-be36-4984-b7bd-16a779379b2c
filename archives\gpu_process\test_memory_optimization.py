#!/usr/bin/env python3
"""
Test script for GPU memory optimization features.
Validates that the memory optimization can handle large datasets like the 66,804 sample case.
"""

import torch
import numpy as np
import time
from typing import Dict, Any
import sys
import os

# Add the current directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_dataset(total_samples: int = 66804, sequence_len: int = 64, 
                       n_features: int = 5, missing_rate: float = 0.3) -> torch.Tensor:
    """
    Create a test dataset similar to the problematic case.
    
    Args:
        total_samples: Number of samples (default: 66,804 like the error case)
        sequence_len: Sequence length
        n_features: Number of features
        missing_rate: Rate of missing values to introduce
        
    Returns:
        Test dataset tensor with missing values
    """
    print(f"🔧 Creating test dataset: {total_samples:,} samples, {sequence_len} seq_len, {n_features} features")
    
    # Create synthetic well log data
    np.random.seed(42)  # For reproducibility
    
    # Generate realistic well log patterns
    depth = np.linspace(0, 1000, total_samples * sequence_len)
    
    # Simulate different log curves with realistic patterns
    gr = 50 + 30 * np.sin(depth * 0.01) + np.random.normal(0, 5, len(depth))  # Gamma Ray
    nphi = 0.2 + 0.1 * np.cos(depth * 0.005) + np.random.normal(0, 0.02, len(depth))  # Neutron Porosity
    rhob = 2.3 + 0.3 * np.sin(depth * 0.008) + np.random.normal(0, 0.05, len(depth))  # Bulk Density
    pe = 3.0 + 0.5 * np.cos(depth * 0.003) + np.random.normal(0, 0.1, len(depth))  # Photoelectric Factor
    target = 0.15 + 0.1 * np.sin(depth * 0.006) + np.random.normal(0, 0.02, len(depth))  # Target log
    
    # Reshape into sequences
    data = np.stack([gr, nphi, rhob, pe, target], axis=1)
    data = data.reshape(total_samples, sequence_len, n_features)
    
    # Introduce missing values
    missing_mask = np.random.random(data.shape) < missing_rate
    data[missing_mask] = np.nan
    
    # Convert to tensor
    tensor = torch.from_numpy(data.astype(np.float32))
    
    print(f"✅ Test dataset created: {tensor.shape}")
    print(f"   • Missing values: {torch.isnan(tensor).sum().item():,} ({missing_rate*100:.1f}%)")
    print(f"   • Memory size: {tensor.numel() * 4 / (1024**2):.1f} MB")
    
    return tensor

def test_memory_estimation():
    """Test memory estimation utilities."""
    print("\n" + "="*60)
    print("🧮 TESTING MEMORY ESTIMATION")
    print("="*60)
    
    try:
        from utils.memory_optimization import get_memory_optimizer, create_memory_report
        
        # Test with the problematic dataset size
        data_shape = (66804, 64, 5)
        model_params = {
            'd_model': 256,
            'n_layers': 2,
            'n_heads': 4
        }
        
        optimizer = get_memory_optimizer()
        
        # Test optimal batch size calculation
        optimal_batch = optimizer.calculate_optimal_batch_size(data_shape, model_params)
        print(f"✅ Optimal batch size calculated: {optimal_batch}")
        
        # Test memory estimation
        memory_est = optimizer.estimate_memory_requirements((optimal_batch, 64, 5), model_params)
        print(f"✅ Memory estimation completed: {memory_est['total_memory_mb']:.1f} MB per batch")
        
        # Create comprehensive report
        report = create_memory_report(data_shape, model_params, optimal_batch)
        print("\n📊 Memory Report:")
        print(report)
        
        return True
        
    except Exception as e:
        print(f"❌ Memory estimation test failed: {e}")
        return False

def test_batch_processing():
    """Test batch processing with memory optimization."""
    print("\n" + "="*60)
    print("🔄 TESTING BATCH PROCESSING")
    print("="*60)
    
    try:
        # Create a smaller test dataset for actual processing
        test_data = create_test_dataset(total_samples=1000, sequence_len=64, n_features=5)
        
        # Test with mock model that simulates SAITS behavior
        class MockSAITSModel:
            def __init__(self):
                self.d_model = 256
                self.n_layers = 2
                self.n_heads = 4
                self.is_fitted = True
            
            def predict(self, data):
                # Simulate prediction by returning data with filled NaNs
                result = data.clone()
                nan_mask = torch.isnan(result)
                result[nan_mask] = torch.randn_like(result[nan_mask]) * 0.1
                return result
            
            def predict_with_memory_optimization(self, data, max_batch_size=None, enable_fallback=True):
                # Import here to avoid circular imports
                from models.advanced_models.base_model import BaseAdvancedModel
                
                # Temporarily inherit the method
                class TempModel(BaseAdvancedModel):
                    def __init__(self, mock_model):
                        # Initialize base class properly
                        super().__init__(
                            n_features=5,
                            sequence_len=64,
                            epochs=50,
                            batch_size=32,
                            learning_rate=1e-3
                        )
                        self.model = mock_model
                        self.is_fitted = True
                        self.d_model = mock_model.d_model
                        self.n_layers = mock_model.n_layers
                        self.n_heads = mock_model.n_heads
                    
                    def _prepare_data(self, data, truth_data=None):
                        return {'X': data.cpu().numpy()}
                    
                    def _initialize_model(self):
                        pass
                
                temp_model = TempModel(self)
                return temp_model.predict_with_memory_optimization(data, max_batch_size, enable_fallback)
        
        model = MockSAITSModel()
        
        print(f"🔮 Testing batch processing with {test_data.shape[0]:,} samples...")
        
        start_time = time.time()
        
        # Test memory-optimized prediction
        result = model.predict_with_memory_optimization(test_data, max_batch_size=32)
        
        end_time = time.time()
        
        print(f"✅ Batch processing completed in {end_time - start_time:.2f} seconds")
        print(f"   • Input shape: {test_data.shape}")
        print(f"   • Output shape: {result.shape}")
        print(f"   • NaN values in result: {torch.isnan(result).sum().item()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Batch processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gpu_fallback():
    """Test GPU fallback mechanisms."""
    print("\n" + "="*60)
    print("🔄 TESTING GPU FALLBACK")
    print("="*60)
    
    try:
        from utils.gpu_fallback import get_fallback_manager
        
        fallback_manager = get_fallback_manager()
        
        # Test device selection
        safe_device = fallback_manager.get_safe_device('cuda')
        print(f"✅ Safe device selected: {safe_device}")
        
        # Test tensor operations with fallback
        test_tensor = torch.randn(100, 64, 5)
        safe_tensor = fallback_manager.safe_to_device(test_tensor, safe_device, "test operation")
        print(f"✅ Tensor safely moved to device: {safe_tensor.device}")
        
        # Test memory clearing
        from utils.gpu_fallback import safe_cuda_empty_cache
        safe_cuda_empty_cache()
        print("✅ CUDA cache cleared safely")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU fallback test failed: {e}")
        return False

def test_large_dataset_simulation():
    """Simulate the exact problematic case: 66,804 samples."""
    print("\n" + "="*60)
    print("🎯 TESTING LARGE DATASET (66,804 SAMPLES)")
    print("="*60)
    
    try:
        # Create the exact problematic dataset size
        large_data = create_test_dataset(total_samples=66804, sequence_len=64, n_features=5)
        
        print(f"📊 Large dataset created: {large_data.shape}")
        print(f"   • Total elements: {large_data.numel():,}")
        print(f"   • Memory size: {large_data.numel() * 4 / (1024**2):.1f} MB")
        
        # Test memory optimization utilities
        from utils.memory_optimization import (
            get_memory_optimizer, 
            adaptive_batch_size_finder,
            create_memory_report
        )
        
        optimizer = get_memory_optimizer()
        
        # Print current memory status
        print("\n🧠 Current memory status:")
        optimizer.print_memory_status()
        
        # Find optimal batch size for this large dataset
        model_params = {'d_model': 256, 'n_layers': 2, 'n_heads': 4}
        
        optimal_batch = adaptive_batch_size_finder(
            large_data.shape, model_params, start_batch_size=32
        )
        
        print(f"\n✅ Optimal batch size for 66,804 samples: {optimal_batch}")
        
        # Create detailed memory report
        report = create_memory_report(large_data.shape, model_params, optimal_batch)
        print(report)
        
        # Estimate processing time
        from utils.memory_optimization import estimate_processing_time
        time_est = estimate_processing_time(large_data.shape, model_params, optimal_batch)
        
        print(f"\n⏱️ Processing time estimates:")
        print(f"   • Total time: {time_est['estimated_total_time_seconds']:.1f} seconds")
        print(f"   • Number of batches: {time_est['estimated_batches']}")
        print(f"   • Time per batch: {time_est['estimated_batch_time_seconds']:.2f} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Large dataset test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all memory optimization tests."""
    print("🚀 MEMORY OPTIMIZATION TEST SUITE")
    print("="*60)
    print("Testing GPU memory optimization for ML Log Prediction")
    print("Specifically validating handling of 66,804 sample datasets")
    print("="*60)
    
    # Setup environment
    try:
        from setup_gpu_environment import configure_pytorch_environment, configure_cuda_settings
        configure_pytorch_environment()
        configure_cuda_settings()
        print("✅ Environment configured")
    except Exception as e:
        print(f"⚠️ Environment setup warning: {e}")
    
    # Run tests
    tests = [
        ("Memory Estimation", test_memory_estimation),
        ("Batch Processing", test_batch_processing),
        ("GPU Fallback", test_gpu_fallback),
        ("Large Dataset (66,804 samples)", test_large_dataset_simulation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Memory optimization is ready for production.")
        print("💡 Your system can now handle large datasets like the 66,804 sample case.")
    else:
        print("⚠️ Some tests failed. Review the output above for details.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
