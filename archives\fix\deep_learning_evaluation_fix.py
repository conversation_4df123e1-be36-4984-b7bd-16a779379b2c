import numpy as np
import pandas as pd
import torch
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
import logging

def evaluate_imputation_and_prediction(
    model, 
    val_sequences_missing, 
    val_sequences_true,
    test_df,
    feature_cols,
    target_col,
    all_features,
    scalers,
    sequence_len=64,
    use_enhanced_preprocessing=True
):
    """
    Properly evaluate deep learning models for both imputation and prediction tasks.
    
    This function addresses the R² discrepancy by:
    1. Evaluating imputation performance on artificially missing values
    2. Evaluating prediction performance on completely held-out data
    3. Providing separate metrics for each task
    
    Args:
        model: Trained deep learning model (SAITS, BRITS, etc.)
        val_sequences_missing: Validation sequences with artificial missing values
        val_sequences_true: Original validation sequences
        test_df: Held-out test dataframe for prediction evaluation
        feature_cols: List of feature column names
        target_col: Target column name
        all_features: List of all features including target
        scalers: Dictionary of scalers for inverse transformation
        sequence_len: Length of sequences
        use_enhanced_preprocessing: Whether to use enhanced preprocessing
        
    Returns:
        dict: Comprehensive evaluation metrics for both tasks
    """
    
    results = {
        'imputation_metrics': {},
        'prediction_metrics': {},
        'combined_metrics': {}
    }
    
    # Convert to tensors if needed
    if not isinstance(val_sequences_missing, torch.Tensor):
        val_sequences_missing = torch.from_numpy(val_sequences_missing.astype(np.float32))
    if not isinstance(val_sequences_true, torch.Tensor):
        val_sequences_true = torch.from_numpy(val_sequences_true.astype(np.float32))
    
    target_idx = all_features.index(target_col)
    
    # ========== PART 1: IMPUTATION EVALUATION ==========
    # This evaluates the model's ability to fill in missing values
    # when surrounding context is available
    
    print("\n📊 Evaluating Imputation Performance...")
    
    # Get model predictions for imputation
    with torch.no_grad():
        if hasattr(model, 'predict'):
            imputed_sequences = model.predict(val_sequences_missing)
        else:
            model.eval()
            imputed_sequences = model(val_sequences_missing)
    
    # Evaluate only on artificially missing values
    val_mask = torch.isnan(val_sequences_missing[:, :, target_idx])
    
    if val_mask.any():
        # Extract values for artificially missing positions only
        y_pred_imputation = imputed_sequences[:, :, target_idx][val_mask].detach().cpu().numpy()
        y_true_imputation = val_sequences_true[:, :, target_idx][val_mask].detach().cpu().numpy()
        
        # Remove any remaining NaNs
        valid_idx = ~np.isnan(y_true_imputation) & ~np.isnan(y_pred_imputation)
        y_pred_imputation = y_pred_imputation[valid_idx]
        y_true_imputation = y_true_imputation[valid_idx]
        
        if len(y_true_imputation) > 0:
            # Calculate imputation metrics
            mae_imp = mean_absolute_error(y_true_imputation, y_pred_imputation)
            r2_imp = r2_score(y_true_imputation, y_pred_imputation)
            rmse_imp = np.sqrt(mean_squared_error(y_true_imputation, y_pred_imputation))
            
            results['imputation_metrics'] = {
                'mae': mae_imp,
                'r2': r2_imp,
                'rmse': rmse_imp,
                'n_samples': len(y_true_imputation),
                'task_description': 'Filling artificial gaps with surrounding context'
            }
            
            print(f"✅ Imputation Metrics (Artificial Missing Values):")
            print(f"   • MAE: {mae_imp:.4f}")
            print(f"   • R²: {r2_imp:.4f}")
            print(f"   • RMSE: {rmse_imp:.4f}")
            print(f"   • Samples: {len(y_true_imputation)}")
    
    # ========== PART 2: PREDICTION EVALUATION ==========
    # This evaluates the model's ability to predict values
    # without target context (more realistic scenario)
    
    print("\n📊 Evaluating Prediction Performance...")
    
    # Prepare test data for prediction (no target context)
    from data_handler import normalize_data, create_sequences
    
    # Normalize test data
    test_df_scaled, _ = normalize_data(test_df, all_features, 
                                       use_enhanced=use_enhanced_preprocessing, 
                                       scalers=scalers)
    
    # Create prediction scenario: features available, target completely missing
    test_pred_df = test_df_scaled.copy()
    test_pred_df[target_col] = np.nan  # Remove all target information
    
    # Create sequences for prediction
    test_sequences, test_metadata = create_sequences(
        test_pred_df, 'WELL', all_features,
        sequence_len=sequence_len, 
        use_enhanced=use_enhanced_preprocessing
    )
    
    if test_sequences.shape[0] > 0:
        # Also create true sequences for comparison
        test_sequences_true, _ = create_sequences(
            test_df_scaled, 'WELL', all_features,
            sequence_len=sequence_len,
            use_enhanced=use_enhanced_preprocessing
        )
        
        # Convert to tensors
        test_tensor = torch.from_numpy(test_sequences.astype(np.float32))
        test_true_tensor = torch.from_numpy(test_sequences_true.astype(np.float32))
        
        # Get predictions
        with torch.no_grad():
            if hasattr(model, 'predict'):
                predicted_sequences = model.predict(test_tensor)
            else:
                model.eval()
                predicted_sequences = model(test_tensor)
        
        # Extract predictions and ground truth for all positions
        y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy().flatten()
        y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy().flatten()
        
        # Remove NaNs
        valid_idx = ~np.isnan(y_true_prediction) & ~np.isnan(y_pred_prediction)
        y_pred_prediction = y_pred_prediction[valid_idx]
        y_true_prediction = y_true_prediction[valid_idx]
        
        if len(y_true_prediction) > 0:
            # Calculate prediction metrics
            mae_pred = mean_absolute_error(y_true_prediction, y_pred_prediction)
            r2_pred = r2_score(y_true_prediction, y_pred_prediction)
            rmse_pred = np.sqrt(mean_squared_error(y_true_prediction, y_pred_prediction))
            
            results['prediction_metrics'] = {
                'mae': mae_pred,
                'r2': r2_pred,
                'rmse': rmse_pred,
                'n_samples': len(y_true_prediction),
                'task_description': 'Predicting without target context'
            }
            
            print(f"✅ Prediction Metrics (No Target Context):")
            print(f"   • MAE: {mae_pred:.4f}")
            print(f"   • R²: {r2_pred:.4f}")
            print(f"   • RMSE: {rmse_pred:.4f}")
            print(f"   • Samples: {len(y_true_prediction)}")
    
    # ========== PART 3: COMBINED ANALYSIS ==========
    
    if results['imputation_metrics'] and results['prediction_metrics']:
        # Calculate performance drop
        r2_drop = results['imputation_metrics']['r2'] - results['prediction_metrics']['r2']
        
        results['combined_metrics'] = {
            'r2_discrepancy': r2_drop,
            'performance_ratio': results['prediction_metrics']['r2'] / results['imputation_metrics']['r2'] if results['imputation_metrics']['r2'] > 0 else 0,
            'task_complexity_increase': results['prediction_metrics']['mae'] / results['imputation_metrics']['mae'] if results['imputation_metrics']['mae'] > 0 else float('inf')
        }
        
        print(f"\n📈 Performance Analysis:")
        print(f"   • R² Drop (Imputation → Prediction): {r2_drop:.4f}")
        print(f"   • Performance Ratio: {results['combined_metrics']['performance_ratio']:.2%}")
        print(f"   • Task Complexity Increase: {results['combined_metrics']['task_complexity_increase']:.2f}x")
        
        # Provide interpretation
        if r2_drop > 0.2:
            print("\n⚠️ SIGNIFICANT PERFORMANCE DROP DETECTED!")
            print("   This indicates the model is optimized for imputation, not prediction.")
            print("   Consider:")
            print("   1. Using this model only for imputation tasks")
            print("   2. Training a separate model for prediction")
            print("   3. Using shallow ML models (XGBoost, etc.) for prediction")
    
    return results


def create_enhanced_evaluation_report(
    all_model_results,
    output_path="model_evaluation_report.txt"
):
    """
    Create a comprehensive evaluation report comparing all models
    on both imputation and prediction tasks.
    
    Args:
        all_model_results: Dict mapping model names to evaluation results
        output_path: Path to save the report
    """
    
    report_lines = [
        "=" * 80,
        "COMPREHENSIVE MODEL EVALUATION REPORT",
        "=" * 80,
        "",
        "This report separates imputation and prediction performance to address",
        "the R² discrepancy issue in deep learning models.",
        "",
        "-" * 80,
        ""
    ]
    
    # Create comparison tables
    imputation_data = []
    prediction_data = []
    
    for model_name, results in all_model_results.items():
        if 'imputation_metrics' in results and results['imputation_metrics']:
            imp = results['imputation_metrics']
            imputation_data.append({
                'Model': model_name,
                'MAE': f"{imp['mae']:.4f}",
                'R²': f"{imp['r2']:.4f}",
                'RMSE': f"{imp['rmse']:.4f}",
                'Samples': imp['n_samples']
            })
        
        if 'prediction_metrics' in results and results['prediction_metrics']:
            pred = results['prediction_metrics']
            prediction_data.append({
                'Model': model_name,
                'MAE': f"{pred['mae']:.4f}",
                'R²': f"{pred['r2']:.4f}",
                'RMSE': f"{pred['rmse']:.4f}",
                'Samples': pred['n_samples']
            })
    
    # Add imputation results
    report_lines.extend([
        "IMPUTATION PERFORMANCE (Filling Missing Values with Context)",
        "-" * 60,
        ""
    ])
    
    if imputation_data:
        df_imp = pd.DataFrame(imputation_data)
        report_lines.append(df_imp.to_string(index=False))
    else:
        report_lines.append("No imputation results available.")
    
    report_lines.extend(["", "", ""])
    
    # Add prediction results
    report_lines.extend([
        "PREDICTION PERFORMANCE (Predicting without Target Context)",
        "-" * 60,
        ""
    ])
    
    if prediction_data:
        df_pred = pd.DataFrame(prediction_data)
        report_lines.append(df_pred.to_string(index=False))
    else:
        report_lines.append("No prediction results available.")
    
    report_lines.extend(["", "", ""])
    
    # Add performance analysis
    report_lines.extend([
        "PERFORMANCE ANALYSIS",
        "-" * 60,
        ""
    ])
    
    for model_name, results in all_model_results.items():
        if 'combined_metrics' in results and results['combined_metrics']:
            cm = results['combined_metrics']
            report_lines.extend([
                f"{model_name}:",
                f"  • R² Discrepancy: {cm['r2_discrepancy']:.4f}",
                f"  • Performance Ratio: {cm['performance_ratio']:.2%}",
                f"  • Task Complexity Increase: {cm['task_complexity_increase']:.2f}x",
                ""
            ])
    
    # Add recommendations
    report_lines.extend([
        "",
        "RECOMMENDATIONS",
        "-" * 60,
        "",
        "1. For IMPUTATION tasks (filling gaps in existing data):",
        "   - Use deep learning models (SAITS, BRITS) if R² > 0.9",
        "   - These models excel at using bidirectional context",
        "",
        "2. For PREDICTION tasks (forecasting without target context):",
        "   - Use shallow ML models (XGBoost, CatBoost, LightGBM)",
        "   - These models better capture feature-target relationships",
        "",
        "3. Model Selection Guidelines:",
        "   - R² discrepancy > 0.2: Model is imputation-optimized",
        "   - R² discrepancy < 0.1: Model generalizes well to prediction",
        "   - Performance ratio < 80%: Consider alternative models for prediction",
        "",
        "=" * 80
    ])
    
    # Write report
    with open(output_path, 'w') as f:
        f.write('\n'.join(report_lines))
    
    print(f"\n📄 Report saved to: {output_path}")
    
    return '\n'.join(report_lines)


# Example integration into existing impute_logs_deep function
def enhanced_impute_logs_deep_evaluation(
    model, 
    model_config,
    train_df, 
    val_df, 
    test_df,
    feature_cols,
    target_col,
    all_features,
    scalers,
    hparams
):
    """
    Enhanced evaluation wrapper that can be integrated into impute_logs_deep.
    
    This provides both imputation and prediction metrics to address
    the R² discrepancy issue.
    """
    
    # ... existing training code ...
    
    # After model training, perform comprehensive evaluation
    evaluation_results = evaluate_imputation_and_prediction(
        model=model,
        val_sequences_missing=val_sequences_missing,  # From existing code
        val_sequences_true=val_sequences_true,        # From existing code
        test_df=test_df,
        feature_cols=feature_cols,
        target_col=target_col,
        all_features=all_features,
        scalers=scalers,
        sequence_len=hparams['sequence_len'],
        use_enhanced_preprocessing=True
    )
    
    # Log results
    logging.info(f"Model: {model_config['name']}")
    logging.info(f"Imputation R²: {evaluation_results['imputation_metrics'].get('r2', 'N/A')}")
    logging.info(f"Prediction R²: {evaluation_results['prediction_metrics'].get('r2', 'N/A')}")
    
    return evaluation_results