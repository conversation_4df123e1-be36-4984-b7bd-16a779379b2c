# GPU Memory Optimization Guide for ML Log Prediction

## Overview

This guide explains how to use the GPU memory optimization features to handle large datasets that exceed GPU memory capacity, specifically addressing the CUDA out of memory error with 66,804 sample datasets.

## Quick Start

### 1. Setup Environment

First, configure your environment for optimal GPU memory usage:

```python
# Run the setup script
python setup_gpu_environment.py
```

This will:
- Set `PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True`
- Configure optimal memory allocation settings
- Initialize memory monitoring
- Display current GPU status

### 2. Test Your Setup

Validate that memory optimization works with your dataset size:

```python
# Test with your exact dataset size
python test_memory_optimization.py
```

This will simulate processing 66,804 samples and show optimal batch sizes.

### 3. Use Memory-Optimized Prediction

When running your ML pipeline, the SAITS model will automatically use memory optimization for large datasets:

```python
# In your main script, large datasets are automatically handled
python main.py
```

## Features

### 1. Automatic Batch Processing

The system automatically splits large datasets into GPU-manageable chunks:

- **Automatic Detection**: Datasets > 1,000 samples trigger memory optimization
- **Optimal Batch Size**: Automatically calculated based on available GPU memory
- **Memory Clearing**: Cache cleared between batches to prevent accumulation

### 2. GPU Memory Monitoring

Real-time monitoring of GPU memory usage:

```python
from utils.memory_optimization import get_memory_optimizer

optimizer = get_memory_optimizer()
optimizer.print_memory_status()  # Shows current GPU/CPU memory usage
```

### 3. Automatic CPU Fallback

If GPU memory is insufficient:

- **Batch-level Fallback**: Individual batches fall back to CPU if needed
- **Model Migration**: Temporarily moves model to CPU for processing
- **Automatic Recovery**: Returns to GPU when possible

### 4. Mixed Precision Training

Reduces memory usage by ~50% when available:

- **Automatic Detection**: Enabled automatically on compatible GPUs
- **Fallback Protection**: Safe fallback to standard precision if issues occur
- **Performance Boost**: Faster training with lower memory usage

## Configuration Options

### Environment Variables

Set these before running your scripts:

```bash
# Essential for large datasets
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

# Optional optimizations
export CUDA_LAUNCH_BLOCKING=0
export CUDA_CACHE_DISABLE=0
```

### Model Parameters

Configure SAITS model for memory efficiency:

```python
# Memory-efficient configuration
hparams = {
    'batch_size': 16,        # Smaller batches for large datasets
    'd_model': 256,          # Balanced model size
    'n_layers': 2,           # Fewer layers for memory efficiency
    'n_heads': 4,            # Reasonable attention heads
    'use_mixed_precision': True,  # Enable if supported
}
```

## Handling the 66,804 Sample Case

### Problem Analysis

Your original error:
```
CUDA out of memory. Tried to allocate 20.00 MiB (GPU 0; 4.00 GiB total capacity; 3.42 GiB already allocated by PyTorch; 0 bytes free; 3.42 GiB reserved in total by PyTorch)
```

### Solution Applied

1. **Batch Processing**: 66,804 samples split into batches of ~8-16 samples
2. **Memory Clearing**: Cache cleared between each batch
3. **Fallback Protection**: CPU processing if GPU fails
4. **Environment Optimization**: Better memory allocation strategy

### Expected Results

With optimization:
- **Batch Size**: ~8-16 samples per batch (auto-calculated)
- **Number of Batches**: ~4,175-8,350 batches
- **Memory Usage**: <1GB per batch instead of >4GB total
- **Processing Time**: 10-30 minutes depending on hardware

## Usage Examples

### Basic Usage (Automatic)

```python
# The system automatically handles large datasets
from ml_core import impute_logs_deep

# This will automatically use memory optimization for large datasets
result_df, metrics = impute_logs_deep(df, feature_cols, target_col, model_config, hparams)
```

### Manual Control

```python
# For manual control over batch processing
from models.advanced_models.saits_model import SAITSModel

model = SAITSModel(**hparams)
model.fit(train_data, truth_data)

# Use memory-optimized prediction with custom batch size
result = model.predict_large_dataset(
    test_data, 
    max_batch_size=8,  # Force smaller batches
    auto_batch_size=False
)
```

### Memory Monitoring

```python
from utils.memory_optimization import get_memory_optimizer, create_memory_report

optimizer = get_memory_optimizer()

# Get detailed memory report
report = create_memory_report(
    data_shape=(66804, 64, 5),
    model_params={'d_model': 256, 'n_layers': 2, 'n_heads': 4},
    batch_size=16
)
print(report)
```

## Troubleshooting

### Still Getting OOM Errors?

1. **Reduce Batch Size**:
   ```python
   # Force smaller batches
   result = model.predict_large_dataset(test_data, max_batch_size=4)
   ```

2. **Enable CPU Fallback**:
   ```python
   # Ensure fallback is enabled (default)
   result = model.predict_large_dataset(test_data, enable_fallback=True)
   ```

3. **Check Environment**:
   ```bash
   # Verify environment variable is set
   echo $PYTORCH_CUDA_ALLOC_CONF
   # Should show: expandable_segments:True
   ```

### Performance Issues?

1. **Monitor Memory Usage**:
   ```python
   optimizer.print_memory_status()  # Before and after processing
   ```

2. **Optimize Batch Size**:
   ```python
   from utils.memory_optimization import adaptive_batch_size_finder
   
   optimal_batch = adaptive_batch_size_finder(
       data_shape=(total_samples, 64, 5),
       model_params=hparams
   )
   ```

3. **Enable Mixed Precision**:
   ```python
   hparams['use_mixed_precision'] = True
   ```

### Memory Leaks?

1. **Clear Cache Manually**:
   ```python
   from utils.gpu_fallback import safe_cuda_empty_cache
   safe_cuda_empty_cache()
   ```

2. **Use Memory Context**:
   ```python
   with optimizer.memory_efficient_context():
       result = model.predict(data)
   ```

## Performance Expectations

### Hardware Requirements

- **Minimum**: 4GB GPU (your current setup)
- **Recommended**: 8GB+ GPU for better performance
- **CPU Fallback**: 16GB+ RAM for large datasets

### Processing Times

For 66,804 samples on 4GB GPU:
- **Optimized**: 10-30 minutes
- **Without optimization**: OOM error
- **CPU fallback**: 30-60 minutes

### Memory Usage

- **Before**: >4GB (causes OOM)
- **After**: <1GB per batch
- **Peak usage**: ~1.5GB total

## Advanced Features

### Custom Memory Strategies

```python
# Custom batch size calculation
from utils.memory_optimization import estimate_batch_memory_usage

safe_batch_size = 1
while estimate_batch_memory_usage(safe_batch_size, 64, 5, hparams) < 500:  # 500MB limit
    safe_batch_size += 1
```

### Performance Monitoring

```python
from utils.performance_monitor import get_performance_monitor

monitor = get_performance_monitor()
monitor.start_monitoring()

# Your processing code here

monitor.stop_monitoring()
monitor.print_performance_report()
```

## Support

If you continue to experience issues:

1. Run the test suite: `python test_memory_optimization.py`
2. Check the memory report for your specific dataset
3. Consider reducing model complexity (d_model, n_layers) for very large datasets
4. Use CPU processing for extremely large datasets that exceed optimization capabilities

The memory optimization system is designed to handle datasets much larger than 66,804 samples, so your specific case should work reliably with these improvements.
