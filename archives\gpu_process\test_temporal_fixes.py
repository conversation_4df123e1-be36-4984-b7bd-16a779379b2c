"""
Comprehensive Test Suite for Temporal Fixes

This test suite validates all the improvements implemented in the ML improvement plan:
1. Flexible data splitting with temporal validation
2. Realistic missing data patterns
3. Enhanced evaluation logic
4. Data leakage detection
5. Performance monitoring with anomaly detection
"""

import pytest
import numpy as np
import pandas as pd
import sys
import os
from unittest.mock import patch, MagicMock

# Add project root to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import modules to test
from ml_core import create_flexible_split, impute_logs_deep
from data_handler import introduce_realistic_missingness, generate_geological_missing_patterns
from data_leakage_detector import (
    detect_perfect_correlation_leakage, 
    validate_temporal_split,
    comprehensive_leakage_check
)
from temporal_validation import WellLogTimeSeriesSplit, perform_temporal_cv
from utils.performance_monitor import ModelPerformanceMonitor


class TestTemporalSplitting:
    """Test suite for temporal splitting functionality."""
    
    @pytest.fixture
    def sample_well_data(self):
        """Create sample well log data for testing."""
        np.random.seed(42)
        
        wells = ['WELL_A', 'WELL_B', 'WELL_C', 'WELL_D']
        data = []
        
        for well in wells:
            n_samples = np.random.randint(100, 200)
            depths = np.linspace(1000, 2000, n_samples)
            
            # Create realistic log curves with some correlation
            gr = 50 + 30 * np.sin(depths / 100) + np.random.normal(0, 5, n_samples)
            nphi = 0.2 + 0.1 * np.cos(depths / 150) + np.random.normal(0, 0.02, n_samples)
            rhob = 2.3 + 0.2 * np.sin(depths / 120) + np.random.normal(0, 0.05, n_samples)
            dt = 100 + 20 * np.cos(depths / 80) + np.random.normal(0, 3, n_samples)
            
            well_data = pd.DataFrame({
                'WELL': well,
                'MD': depths,
                'GR': gr,
                'NPHI': nphi,
                'RHOB': rhob,
                'DT': dt
            })
            data.append(well_data)
        
        return pd.concat(data, ignore_index=True)
    
    def test_create_flexible_split_basic(self, sample_well_data):
        """Test basic functionality of create_flexible_split."""
        train_wells = ['WELL_A', 'WELL_B']
        test_wells = ['WELL_C', 'WELL_D']
        
        train_df, val_df, test_df = create_flexible_split(
            sample_well_data,
            train_wells=train_wells,
            test_wells=test_wells,
            val_depth_ratio=0.3
        )
        
        # Check that splits are non-empty
        assert len(train_df) > 0, "Training set should not be empty"
        assert len(val_df) > 0, "Validation set should not be empty"
        assert len(test_df) > 0, "Test set should not be empty"
        
        # Check well assignments
        assert set(train_df['WELL'].unique()).issubset(set(train_wells))
        assert set(val_df['WELL'].unique()).issubset(set(train_wells))
        assert set(test_df['WELL'].unique()) == set(test_wells)
        
        # Check temporal ordering within training wells
        for well in train_wells:
            if well in train_df['WELL'].values and well in val_df['WELL'].values:
                train_max_depth = train_df[train_df['WELL'] == well]['MD'].max()
                val_min_depth = val_df[val_df['WELL'] == well]['MD'].min()
                assert train_max_depth <= val_min_depth, f"Temporal ordering violated for {well}"
    
    def test_create_flexible_split_error_handling(self, sample_well_data):
        """Test error handling in create_flexible_split."""
        # Test missing parameters
        with pytest.raises(ValueError, match="You must provide a list"):
            create_flexible_split(sample_well_data, train_wells=None, test_wells=['WELL_A'])
        
        # Test overlapping wells
        with pytest.raises(ValueError, match="Train and test wells must not overlap"):
            create_flexible_split(
                sample_well_data,
                train_wells=['WELL_A', 'WELL_B'],
                test_wells=['WELL_B', 'WELL_C']
            )
    
    def test_temporal_validation_split(self, sample_well_data):
        """Test WellLogTimeSeriesSplit functionality."""
        splitter = WellLogTimeSeriesSplit(n_splits=3, test_size=0.2)
        
        splits = list(splitter.split(sample_well_data))
        
        assert len(splits) > 0, "Should generate at least one split"
        assert len(splits) <= 3, "Should not exceed requested number of splits"
        
        for train_idx, test_idx in splits:
            # Check no overlap between train and test
            assert len(set(train_idx) & set(test_idx)) == 0, "Train and test indices should not overlap"
            
            # Check temporal ordering
            train_data = sample_well_data.loc[train_idx]
            test_data = sample_well_data.loc[test_idx]
            
            for well in train_data['WELL'].unique():
                if well in test_data['WELL'].values:
                    train_max_depth = train_data[train_data['WELL'] == well]['MD'].max()
                    test_min_depth = test_data[test_data['WELL'] == well]['MD'].min()
                    assert train_max_depth <= test_min_depth, f"Temporal ordering violated for {well}"


class TestRealisticMissingness:
    """Test suite for realistic missing data patterns."""
    
    @pytest.fixture
    def sample_sequences(self):
        """Create sample sequences for testing."""
        np.random.seed(42)
        n_sequences, seq_len, n_features = 10, 64, 4
        sequences = np.random.randn(n_sequences, seq_len, n_features)
        return sequences
    
    def test_introduce_realistic_missingness_basic(self, sample_sequences):
        """Test basic functionality of introduce_realistic_missingness."""
        missing_sequences = introduce_realistic_missingness(
            sample_sequences,
            missing_rate=0.2,
            pattern_type='mixed'
        )
        
        # Check shape preservation
        assert missing_sequences.shape == sample_sequences.shape
        
        # Check that missing values were introduced
        original_missing = np.isnan(sample_sequences).sum()
        new_missing = np.isnan(missing_sequences).sum()
        assert new_missing > original_missing, "Missing values should have been introduced"
        
        # Check missing rate is approximately correct
        total_elements = np.prod(sample_sequences.shape)
        actual_missing_rate = new_missing / total_elements
        assert 0.15 <= actual_missing_rate <= 0.25, f"Missing rate {actual_missing_rate} not in expected range"
    
    def test_geological_missing_patterns(self, sample_sequences):
        """Test geological missing pattern generation."""
        missing_sequences = generate_geological_missing_patterns(
            sample_sequences,
            missing_rate=0.15
        )
        
        # Check shape preservation
        assert missing_sequences.shape == sample_sequences.shape
        
        # Check that missing values were introduced
        original_missing = np.isnan(sample_sequences).sum()
        new_missing = np.isnan(missing_sequences).sum()
        assert new_missing > original_missing, "Missing values should have been introduced"
    
    def test_different_pattern_types(self, sample_sequences):
        """Test different missing pattern types."""
        pattern_types = ['mixed', 'geological', 'operational']
        
        for pattern_type in pattern_types:
            missing_sequences = introduce_realistic_missingness(
                sample_sequences,
                missing_rate=0.2,
                pattern_type=pattern_type
            )
            
            # Check that each pattern type produces different results
            assert missing_sequences.shape == sample_sequences.shape
            assert np.isnan(missing_sequences).sum() > np.isnan(sample_sequences).sum()


class TestDataLeakageDetection:
    """Test suite for data leakage detection functionality."""
    
    @pytest.fixture
    def clean_data_splits(self):
        """Create clean data splits without leakage."""
        np.random.seed(42)
        n_samples = 1000
        
        # Create features with some correlation but no perfect leakage
        x1 = np.random.randn(n_samples)
        x2 = np.random.randn(n_samples)
        target = 0.5 * x1 + 0.3 * x2 + np.random.randn(n_samples) * 0.5
        
        df = pd.DataFrame({
            'WELL': ['WELL_A'] * (n_samples // 2) + ['WELL_B'] * (n_samples // 2),
            'MD': np.concatenate([np.linspace(1000, 2000, n_samples // 2),
                                 np.linspace(1000, 2000, n_samples // 2)]),
            'FEATURE1': x1,
            'FEATURE2': x2,
            'TARGET': target
        })
        
        # Create proper temporal splits
        train_df = df[df['MD'] < 1500].copy()
        val_df = df[(df['MD'] >= 1500) & (df['MD'] < 1750)].copy()
        test_df = df[df['MD'] >= 1750].copy()
        
        return train_df, val_df, test_df
    
    @pytest.fixture
    def leaky_data_splits(self):
        """Create data splits with intentional leakage."""
        np.random.seed(42)
        n_samples = 1000
        
        # Create features with perfect correlation (leakage)
        target = np.random.randn(n_samples)
        leaky_feature = target + np.random.randn(n_samples) * 0.01  # Almost perfect correlation
        normal_feature = np.random.randn(n_samples)
        
        df = pd.DataFrame({
            'WELL': ['WELL_A'] * (n_samples // 2) + ['WELL_B'] * (n_samples // 2),
            'MD': np.concatenate([np.linspace(1000, 2000, n_samples // 2),
                                 np.linspace(1000, 2000, n_samples // 2)]),
            'LEAKY_FEATURE': leaky_feature,
            'NORMAL_FEATURE': normal_feature,
            'TARGET': target
        })
        
        # Create splits
        train_df = df[df['MD'] < 1500].copy()
        val_df = df[(df['MD'] >= 1500) & (df['MD'] < 1750)].copy()
        test_df = df[df['MD'] >= 1750].copy()
        
        return train_df, val_df, test_df
    
    def test_detect_perfect_correlation_clean_data(self, clean_data_splits):
        """Test leakage detection on clean data."""
        train_df, val_df, test_df = clean_data_splits
        
        results = detect_perfect_correlation_leakage(
            train_df, val_df, test_df,
            feature_cols=['FEATURE1', 'FEATURE2'],
            target_col='TARGET'
        )
        
        assert not results['leakage_detected'], "Should not detect leakage in clean data"
        assert len(results['high_correlations']) == 0, "Should not find high correlations"
    
    def test_detect_perfect_correlation_leaky_data(self, leaky_data_splits):
        """Test leakage detection on data with intentional leakage."""
        train_df, val_df, test_df = leaky_data_splits
        
        results = detect_perfect_correlation_leakage(
            train_df, val_df, test_df,
            feature_cols=['LEAKY_FEATURE', 'NORMAL_FEATURE'],
            target_col='TARGET',
            threshold=0.9
        )
        
        assert results['leakage_detected'], "Should detect leakage in leaky data"
        assert 'LEAKY_FEATURE' in results['suspicious_features'], "Should identify leaky feature"
    
    def test_validate_temporal_split_clean(self, clean_data_splits):
        """Test temporal split validation on properly ordered data."""
        train_df, val_df, test_df = clean_data_splits
        
        results = validate_temporal_split(train_df, val_df, test_df)
        
        assert not results['temporal_leakage_detected'], "Should not detect temporal leakage"
        assert len(results['violations']) == 0, "Should not find temporal violations"
    
    def test_comprehensive_leakage_check(self, clean_data_splits):
        """Test comprehensive leakage check functionality."""
        train_df, val_df, test_df = clean_data_splits
        
        results = comprehensive_leakage_check(
            train_df, val_df, test_df,
            feature_cols=['FEATURE1', 'FEATURE2'],
            target_col='TARGET'
        )
        
        assert not results['overall_leakage_detected'], "Should not detect overall leakage"
        assert results['summary']['data_quality_score'] > 0.8, "Should have high data quality score"


class TestPerformanceMonitoring:
    """Test suite for enhanced performance monitoring."""
    
    def test_model_performance_monitor_initialization(self):
        """Test ModelPerformanceMonitor initialization."""
        monitor = ModelPerformanceMonitor(enable_anomaly_detection=True)
        
        assert monitor.enable_anomaly_detection is True
        assert len(monitor.model_metrics_history) == 0
        assert 'r2_suspicious' in monitor.anomaly_thresholds
    
    def test_log_normal_performance(self):
        """Test logging normal model performance."""
        monitor = ModelPerformanceMonitor()
        
        # Log normal performance metrics
        metrics = {'mae': 0.1, 'r2': 0.75, 'rmse': 0.15}
        record = monitor.log_model_performance('test_model', metrics)
        
        assert len(record['anomalies_detected']) == 0, "Should not detect anomalies in normal performance"
        assert record['anomaly_score'] == 0.0, "Anomaly score should be zero"
    
    def test_log_suspicious_performance(self):
        """Test logging suspicious model performance."""
        monitor = ModelPerformanceMonitor()
        
        # Log suspicious performance metrics
        metrics = {'mae': 0.001, 'r2': 0.99, 'rmse': 0.001}
        record = monitor.log_model_performance('suspicious_model', metrics)
        
        assert len(record['anomalies_detected']) > 0, "Should detect anomalies in suspicious performance"
        assert record['anomaly_score'] > 0.5, "Anomaly score should be high"
    
    def test_anomaly_summary(self):
        """Test anomaly summary generation."""
        monitor = ModelPerformanceMonitor()
        
        # Log multiple models with different performance levels
        monitor.log_model_performance('normal_model', {'mae': 0.1, 'r2': 0.75, 'rmse': 0.15})
        monitor.log_model_performance('suspicious_model', {'mae': 0.001, 'r2': 0.99, 'rmse': 0.001})
        
        summary = monitor.get_anomaly_summary()
        
        assert summary['total_models_monitored'] == 2
        assert summary['models_with_anomalies'] >= 1
        assert len(summary['recommendations']) > 0


def test_integration_workflow():
    """Test the complete integration workflow."""
    # Create sample data
    np.random.seed(42)
    wells = ['WELL_A', 'WELL_B', 'WELL_C']
    data = []
    
    for well in wells:
        n_samples = 150
        depths = np.linspace(1000, 2000, n_samples)
        
        well_data = pd.DataFrame({
            'WELL': well,
            'MD': depths,
            'GR': 50 + np.random.normal(0, 10, n_samples),
            'NPHI': 0.2 + np.random.normal(0, 0.05, n_samples),
            'RHOB': 2.3 + np.random.normal(0, 0.1, n_samples),
            'DT': 100 + np.random.normal(0, 10, n_samples)
        })
        data.append(well_data)
    
    df = pd.concat(data, ignore_index=True)
    
    # Test flexible splitting
    train_wells = ['WELL_A', 'WELL_B']
    test_wells = ['WELL_C']
    
    train_df, val_df, test_df = create_flexible_split(
        df, train_wells=train_wells, test_wells=test_wells
    )
    
    # Test leakage detection
    leakage_results = comprehensive_leakage_check(
        train_df, val_df, test_df,
        feature_cols=['GR', 'NPHI', 'RHOB'],
        target_col='DT'
    )
    
    # Test performance monitoring
    monitor = ModelPerformanceMonitor()
    monitor.log_model_performance('integration_test', {'mae': 0.1, 'r2': 0.8, 'rmse': 0.12})
    
    # Verify integration works
    assert len(train_df) > 0
    assert len(val_df) > 0
    assert len(test_df) > 0
    assert not leakage_results['overall_leakage_detected']
    assert len(monitor.model_metrics_history) == 1


if __name__ == "__main__":
    # Run tests if script is executed directly
    pytest.main([__file__, "-v"])
