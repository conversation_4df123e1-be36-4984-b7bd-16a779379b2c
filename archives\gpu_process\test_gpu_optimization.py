#!/usr/bin/env python3
"""
GPU Optimization Test Script for ML Log Prediction
Tests GPU acceleration, fallback mechanisms, and performance improvements.
"""

import torch
import numpy as np
import pandas as pd
import time
import sys
import os
from typing import Dict, Any

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gpu_availability():
    """Test GPU availability and basic CUDA operations."""
    print("🔍 Testing GPU Availability...")
    print(f"   • PyTorch version: {torch.__version__}")
    print(f"   • CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"   • CUDA version: {torch.version.cuda}")
        print(f"   • GPU count: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"   • GPU {i}: {props.name}")
            print(f"     - Memory: {props.total_memory / 1e9:.1f} GB")
            print(f"     - Compute Capability: {props.major}.{props.minor}")
        
        # Test basic GPU operations
        try:
            test_tensor = torch.randn(1000, 1000).cuda()
            result = torch.matmul(test_tensor, test_tensor.T)
            print("   ✅ Basic GPU operations working")
            return True
        except Exception as e:
            print(f"   ❌ GPU operations failed: {e}")
            return False
    else:
        print("   ⚠️ CUDA not available - will test CPU fallback")
        return False

def test_gpu_utilities():
    """Test GPU utility modules."""
    print("\n🔧 Testing GPU Utilities...")
    
    try:
        from utils.gpu_acceleration import GPUManager
        from utils.optimization import GPUAccelerator
        from utils.performance_monitor import get_performance_monitor
        from utils.gpu_fallback import get_fallback_manager
        
        print("   ✅ All GPU utility modules imported successfully")
        
        # Test GPU Manager
        try:
            gpu_manager = GPUManager()
            print(f"   ✅ GPU Manager initialized: {gpu_manager.device}")
        except Exception as e:
            print(f"   ⚠️ GPU Manager failed: {e}")
        
        # Test Performance Monitor
        try:
            monitor = get_performance_monitor()
            print("   ✅ Performance Monitor initialized")
        except Exception as e:
            print(f"   ⚠️ Performance Monitor failed: {e}")
        
        # Test Fallback Manager
        try:
            fallback = get_fallback_manager()
            safe_device = fallback.get_safe_device()
            print(f"   ✅ Fallback Manager initialized: {safe_device}")
        except Exception as e:
            print(f"   ⚠️ Fallback Manager failed: {e}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ GPU utilities import failed: {e}")
        return False

def test_deep_learning_models():
    """Test GPU-optimized deep learning models."""
    print("\n🧠 Testing Deep Learning Models...")
    
    try:
        from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet
        
        # Create sample data
        batch_size = 32
        sequence_len = 64
        n_features = 4
        
        # Generate synthetic well log data
        np.random.seed(42)
        train_data = torch.randn(batch_size, sequence_len, n_features)
        truth_data = train_data.clone()
        
        # Introduce some missing values
        missing_mask = torch.rand_like(train_data) < 0.3
        train_data[missing_mask] = float('nan')
        
        print(f"   • Sample data shape: {train_data.shape}")
        print(f"   • Missing data: {missing_mask.float().mean().item():.1%}")
        
        # Test SimpleAutoencoder
        print("\n   Testing SimpleAutoencoder...")
        try:
            autoencoder = SimpleAutoencoder(
                n_features=n_features,
                sequence_len=sequence_len,
                epochs=5,  # Short test
                batch_size=16,
                use_mixed_precision=True
            )
            
            print(f"     • Device: {autoencoder.device}")
            print(f"     • Mixed precision: {autoencoder.scaler is not None}")
            
            # Test training
            start_time = time.time()
            autoencoder.fit(train_data, truth_data)
            training_time = time.time() - start_time
            
            print(f"     • Training completed in {training_time:.2f}s")
            
            # Test prediction
            start_time = time.time()
            predictions = autoencoder.predict(train_data)
            prediction_time = time.time() - start_time
            
            print(f"     • Prediction completed in {prediction_time:.3f}s")
            print(f"     • Output shape: {predictions.shape}")
            print("     ✅ SimpleAutoencoder test passed")
            
        except Exception as e:
            print(f"     ❌ SimpleAutoencoder test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test SimpleUNet
        print("\n   Testing SimpleUNet...")
        try:
            unet = SimpleUNet(
                n_features=n_features,
                sequence_len=sequence_len,
                epochs=3,  # Short test
                batch_size=16,
                use_mixed_precision=True
            )
            
            print(f"     • Device: {unet.device}")
            print(f"     • Mixed precision: {unet.scaler is not None}")
            
            # Test training
            start_time = time.time()
            unet.fit(train_data, truth_data)
            training_time = time.time() - start_time
            
            print(f"     • Training completed in {training_time:.2f}s")
            
            # Test prediction
            start_time = time.time()
            predictions = unet.predict(train_data)
            prediction_time = time.time() - start_time
            
            print(f"     • Prediction completed in {prediction_time:.3f}s")
            print(f"     • Output shape: {predictions.shape}")
            print("     ✅ SimpleUNet test passed")
            
        except Exception as e:
            print(f"     ❌ SimpleUNet test failed: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Deep learning models import failed: {e}")
        return False

def test_gradient_boosting_gpu():
    """Test GPU optimization for gradient boosting models."""
    print("\n🌳 Testing Gradient Boosting GPU Optimization...")
    
    try:
        from ml_core import create_gpu_optimized_model, MODEL_REGISTRY
        
        # Create sample data
        np.random.seed(42)
        X = np.random.randn(1000, 10)
        y = np.random.randn(1000)
        
        print(f"   • Sample data: {X.shape[0]} samples, {X.shape[1]} features")
        
        # Test each gradient boosting model
        for model_key in ['xgboost', 'lightgbm', 'catboost']:
            if model_key in MODEL_REGISTRY:
                print(f"\n   Testing {model_key}...")
                try:
                    # Get default hyperparameters
                    hparams = {}
                    for hp_name, hp_config in MODEL_REGISTRY[model_key]['hyperparameters'].items():
                        hparams[hp_name] = hp_config['default']
                    
                    # Create GPU-optimized model
                    model = create_gpu_optimized_model(model_key, hparams)
                    
                    # Test training
                    start_time = time.time()
                    model.fit(X, y)
                    training_time = time.time() - start_time
                    
                    print(f"     • Training completed in {training_time:.2f}s")
                    
                    # Test prediction
                    start_time = time.time()
                    predictions = model.predict(X)
                    prediction_time = time.time() - start_time
                    
                    print(f"     • Prediction completed in {prediction_time:.3f}s")
                    print(f"     • Output shape: {predictions.shape}")
                    print(f"     ✅ {model_key} test passed")
                    
                except Exception as e:
                    print(f"     ❌ {model_key} test failed: {e}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ ML core import failed: {e}")
        return False

def test_performance_monitoring():
    """Test performance monitoring functionality."""
    print("\n📊 Testing Performance Monitoring...")
    
    try:
        from utils.performance_monitor import get_performance_monitor
        
        monitor = get_performance_monitor()
        
        # Test monitoring
        monitor.start_monitoring()
        print("   • Monitoring started")
        
        # Simulate some work
        if torch.cuda.is_available():
            # GPU work
            with monitor.monitor_training_epoch():
                tensor = torch.randn(1000, 1000).cuda()
                result = torch.matmul(tensor, tensor.T)
                time.sleep(1)  # Simulate training time
        else:
            # CPU work
            with monitor.monitor_training_epoch():
                tensor = torch.randn(1000, 1000)
                result = torch.matmul(tensor, tensor.T)
                time.sleep(1)  # Simulate training time
        
        monitor.stop_monitoring()
        print("   • Monitoring stopped")
        
        # Get performance summary
        summary = monitor.get_performance_summary()
        print(f"   • Monitoring duration: {summary['monitoring_duration']:.1f}s")
        
        if summary['gpu_stats'] and torch.cuda.is_available():
            gpu = summary['gpu_stats']
            print(f"   • GPU utilization: {gpu['avg_utilization']:.1f}%")
            print(f"   • GPU memory used: {gpu['avg_memory_used']:.1f}GB")
        
        if summary['training_stats']:
            train = summary['training_stats']
            print(f"   • Training epochs: {train['total_epochs']}")
            print(f"   • Avg epoch time: {train['avg_epoch_time']:.2f}s")
        
        print("   ✅ Performance monitoring test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Performance monitoring test failed: {e}")
        return False

def test_fallback_mechanisms():
    """Test GPU fallback mechanisms."""
    print("\n🔄 Testing Fallback Mechanisms...")
    
    try:
        from utils.gpu_fallback import get_fallback_manager
        
        fallback = get_fallback_manager()
        
        # Test safe device selection
        safe_device = fallback.get_safe_device('cuda')
        print(f"   • Safe device: {safe_device}")
        
        # Test safe tensor operations
        test_tensor = torch.randn(100, 100)
        safe_tensor = fallback.safe_to_device(test_tensor, 'cuda', 'test operation')
        print(f"   • Safe tensor device: {safe_tensor.device}")
        
        # Test safe scaler creation
        scaler = fallback.create_safe_scaler('cuda')
        print(f"   • Safe scaler: {scaler is not None}")
        
        # Test safe autocast context
        autocast_ctx = fallback.safe_autocast_context('cuda')
        print(f"   • Safe autocast: {autocast_ctx is not None}")
        
        # Get fallback summary
        summary = fallback.get_fallback_summary()
        print(f"   • Total fallbacks: {summary['total_fallbacks']}")
        print(f"   • GPU available: {summary['gpu_available']}")
        
        print("   ✅ Fallback mechanisms test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Fallback mechanisms test failed: {e}")
        return False

def main():
    """Run comprehensive GPU optimization tests."""
    print("🚀 GPU OPTIMIZATION TEST SUITE")
    print("=" * 60)
    
    test_results = {}
    
    # Run all tests
    test_results['gpu_availability'] = test_gpu_availability()
    test_results['gpu_utilities'] = test_gpu_utilities()
    test_results['deep_learning'] = test_deep_learning_models()
    test_results['gradient_boosting'] = test_gradient_boosting_gpu()
    test_results['performance_monitoring'] = test_performance_monitoring()
    test_results['fallback_mechanisms'] = test_fallback_mechanisms()
    
    # Print summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title():<25} {status}")
    
    print("-" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! GPU optimization is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
