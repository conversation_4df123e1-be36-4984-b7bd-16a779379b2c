import re

print("🔧 Applying Broadcasting Error Fix...")

# Read the file
with open('ml_core.py', 'r', encoding='utf-8') as f:
    content = f.read()

original_content = content

# Fix 1: Remove flatten() from prediction evaluation
content = content.replace(
    'y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy().flatten()',
    'y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy()'
)

content = content.replace(
    'y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy().flatten()',
    'y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy()\n\n        # Safe array alignment to prevent broadcasting errors\n        y_pred_prediction, y_true_prediction = safe_array_alignment(\n            y_pred_prediction, y_true_prediction, "prediction evaluation"\n        )'
)

# Check if changes were made
if content != original_content:
    # Write back
    with open('ml_core.py', 'w', encoding='utf-8') as f:
        f.write(content)
    print('✅ Broadcasting fix applied successfully!')
    print('   • Removed .flatten() from prediction arrays')
    print('   • Added safe_array_alignment call')
    print('   • SAITS and BRITS models should now work!')
else:
    print('⚠️ No changes made - fix may already be applied')