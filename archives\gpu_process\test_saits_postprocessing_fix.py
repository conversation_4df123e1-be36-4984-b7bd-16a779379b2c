#!/usr/bin/env python3
"""
Test script to verify the SAITS post-processing fix.
This script creates synthetic data and tests the post-processing pipeline.
"""

import numpy as np
import pandas as pd
import torch
import sys
import os

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_synthetic_well_data(n_wells=3, n_points_per_well=200, n_features=5):
    """Create synthetic well log data for testing."""
    print(f"Creating synthetic data: {n_wells} wells, {n_points_per_well} points each, {n_features} features")
    
    all_data = []
    
    for well_idx in range(n_wells):
        well_name = f"WELL_{well_idx + 1}"
        
        # Create depth values
        md_values = np.linspace(1000 + well_idx * 500, 1000 + well_idx * 500 + n_points_per_well * 0.5, n_points_per_well)
        
        # Create synthetic log data with some realistic patterns
        np.random.seed(42 + well_idx)  # For reproducible results
        
        # Feature 1: GR (Gamma Ray) - typically 0-200 API
        gr = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, n_points_per_well)) + np.random.normal(0, 5, n_points_per_well)
        gr = np.clip(gr, 0, 200)
        
        # Feature 2: RHOB (Bulk Density) - typically 1.5-3.0 g/cc
        rhob = 2.3 + 0.3 * np.cos(np.linspace(0, 3*np.pi, n_points_per_well)) + np.random.normal(0, 0.05, n_points_per_well)
        rhob = np.clip(rhob, 1.5, 3.0)
        
        # Feature 3: NPHI (Neutron Porosity) - typically 0-0.5 v/v
        nphi = 0.15 + 0.1 * np.sin(np.linspace(0, 5*np.pi, n_points_per_well)) + np.random.normal(0, 0.02, n_points_per_well)
        nphi = np.clip(nphi, 0, 0.5)
        
        # Feature 4: RT (Resistivity) - log-normal distribution
        rt = np.exp(1 + 2 * np.sin(np.linspace(0, 2*np.pi, n_points_per_well)) + np.random.normal(0, 0.3, n_points_per_well))
        rt = np.clip(rt, 0.1, 1000)
        
        # Target: P-WAVE (Compressional Wave Velocity) - typically 5000-20000 ft/s
        # Make it correlated with other logs for realistic behavior
        pwave = 8000 + 2000 * (rhob - 2.3) / 0.3 - 5000 * nphi + 500 * np.log10(rt) + np.random.normal(0, 200, n_points_per_well)
        pwave = np.clip(pwave, 5000, 20000)
        
        # Introduce some missing values in the target (P-WAVE)
        missing_indices = np.random.choice(n_points_per_well, size=int(0.3 * n_points_per_well), replace=False)
        pwave[missing_indices] = np.nan
        
        # Create DataFrame for this well
        well_data = pd.DataFrame({
            'WELL': well_name,
            'MD': md_values,
            'GR': gr,
            'RHOB': rhob,
            'NPHI': nphi,
            'RT': rt,
            'PWAVE': pwave
        })
        
        all_data.append(well_data)
    
    # Combine all wells
    df = pd.concat(all_data, ignore_index=True)
    print(f"✅ Created synthetic dataset with {len(df)} total points")
    print(f"   Missing P-WAVE values: {df['PWAVE'].isna().sum()} ({df['PWAVE'].isna().mean()*100:.1f}%)")
    
    return df

def test_saits_postprocessing():
    """Test the SAITS model post-processing with our fix."""
    print("\n🧪 Testing SAITS Post-processing Fix...")
    
    try:
        # Import required modules
        from ml_core import impute_logs_deep
        from data_handler import normalize_data
        
        # Create synthetic data
        df = create_synthetic_well_data(n_wells=3, n_points_per_well=100, n_features=5)
        
        # Define features and target
        feature_cols = ['GR', 'RHOB', 'NPHI', 'RT']
        target_col = 'PWAVE'
        
        print(f"\n📊 Data Summary:")
        print(f"   Total points: {len(df)}")
        print(f"   Wells: {df['WELL'].unique().tolist()}")
        print(f"   Features: {feature_cols}")
        print(f"   Target: {target_col}")
        print(f"   Missing target values: {df[target_col].isna().sum()}")
        
        # Test with SAITS model
        print(f"\n🚀 Testing SAITS model...")
        
        # Configure hyperparameters for quick testing
        hyperparams = {
            'sequence_len': 32,  # Smaller for faster testing
            'epochs': 5,         # Fewer epochs for quick test
            'batch_size': 16,
            'learning_rate': 1e-3,
            'n_layers': 2,
            'd_model': 64,       # Smaller model for testing
            'n_heads': 4
        }
        
        # Configure well separation (use all wells for training and prediction)
        well_config = {
            'mode': 'combined',
            'training_wells': df['WELL'].unique().tolist(),
            'prediction_wells': df['WELL'].unique().tolist()
        }
        
        # Get SAITS model configuration
        from ml_core import MODEL_REGISTRY
        model_config = MODEL_REGISTRY['saits']

        # Run SAITS imputation
        result_df, metadata = impute_logs_deep(
            df=df,
            feature_cols=feature_cols,
            target_col=target_col,
            model_config=model_config,
            hparams=hyperparams,
            use_enhanced_preprocessing=True
        )
        
        if result_df is not None and metadata is not None:
            print(f"✅ SAITS post-processing completed successfully!")
            
            # Check results
            pred_col = f'{target_col}_pred'
            imp_col = f'{target_col}_imputed'
            
            if pred_col in result_df.columns:
                pred_count = result_df[pred_col].notna().sum()
                print(f"   Predictions generated: {pred_count}")
                
            if imp_col in result_df.columns:
                imp_count = result_df[imp_col].notna().sum()
                original_count = df[target_col].notna().sum()
                print(f"   Original non-NaN values: {original_count}")
                print(f"   Imputed non-NaN values: {imp_count}")
                print(f"   Values filled: {imp_count - original_count}")
            
            return True
        else:
            print(f"❌ SAITS imputation returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error during SAITS testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the post-processing fix test."""
    print("=" * 60)
    print(" SAITS POST-PROCESSING FIX TEST")
    print("=" * 60)
    
    # Test SAITS post-processing
    success = test_saits_postprocessing()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ ALL TESTS PASSED - Post-processing fix is working!")
    else:
        print("❌ TESTS FAILED - Post-processing fix needs more work")
    print("=" * 60)

if __name__ == "__main__":
    main()
