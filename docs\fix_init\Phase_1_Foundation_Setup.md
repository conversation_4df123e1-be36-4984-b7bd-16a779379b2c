# Phase 1: Foundation Setup (Week 1)
## Advanced Deep Learning Models Implementation - Foundation Phase

### 🎯 **Phase Objectives**
- Install and test PyPOTS dependencies without affecting existing functionality
- Create advanced_models directory structure
- Implement safe model loading mechanisms with graceful fallbacks
- Establish backward compatibility safeguards

### 📋 **Phase 1 Tasks Breakdown**

#### **Day 1-2: Environment Setup & Dependency Management**

##### Task 1.1: Dependency Installation & Testing
```bash
# Core PyPOTS framework for advanced models
pip install pypots==0.1.4

# MONAI for enhanced U-Net implementation  
pip install monai>=1.3.0

# Additional dependencies for transformer models
pip install transformers>=4.30.0
pip install einops>=0.7.0

# Optional: For enhanced attention mechanisms
pip install torch-audio>=0.13.0
```

**Validation Steps:**
```python
# Test PyPOTS installation
python -c "from pypots.imputation import SAITS, BRITS; print('✅ PyPOTS models available')"

# Test MONAI installation
python -c "from monai.networks.nets import UNet; print('✅ MONAI UNet available')"

# Test transformers
python -c "import transformers; print('✅ Transformers available')"

# Test einops
python -c "import einops; print('✅ Einops available')"
```

**Safety Measures:**
- Create virtual environment backup before installation
- Test existing models functionality after each dependency installation
- Document any version conflicts and resolution steps

##### Task 1.2: Backward Compatibility Testing
```python
# Create test script: test_backward_compatibility.py
def test_existing_models_unchanged():
    """Ensure existing models work exactly as before"""
    from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet
    import torch
    import numpy as np
    
    print("🔍 Testing SimpleAutoencoder...")
    # Test SimpleAutoencoder functionality
    model = SimpleAutoencoder(n_features=4, sequence_len=64, epochs=2)
    test_data = torch.randn(10, 64, 4)
    test_data[test_data > 0.5] = float('nan')  # Add missing values
    truth_data = torch.randn(10, 64, 4)
    
    # Train and predict
    model.fit(test_data, truth_data, epochs=2, batch_size=5)
    predictions = model.predict(test_data)
    
    assert predictions.shape == test_data.shape, "SimpleAutoencoder output shape mismatch"
    print("✅ SimpleAutoencoder working correctly")
    
    print("🔍 Testing SimpleUNet...")
    # Test SimpleUNet functionality  
    unet_model = SimpleUNet(n_features=4, sequence_len=64, epochs=2)
    unet_model.fit(test_data, truth_data, epochs=2, batch_size=5)
    unet_predictions = unet_model.predict(test_data)
    
    assert unet_predictions.shape == test_data.shape, "SimpleUNet output shape mismatch"
    print("✅ SimpleUNet working correctly")
    
    print("✅ All existing models pass backward compatibility test")

if __name__ == "__main__":
    test_existing_models_unchanged()
```

#### **Day 3-4: Directory Structure & Safe Loading Framework**

##### Task 1.3: Create Advanced Models Directory Structure
```
models/
├── __init__.py                   # UNCHANGED - existing imports
├── simple_autoencoder.py         # UNCHANGED - existing implementation
├── autoencoder.py                # UNCHANGED - PyPOTS-based implementation  
├── unet.py                       # UNCHANGED - existing MONAI implementation
├── neuralnet.py                  # UNCHANGED - existing implementation
└── advanced_models/              # NEW DIRECTORY
    ├── __init__.py               # NEW - Advanced model imports
    ├── base_model.py             # NEW - Base class for advanced models
    ├── saits_model.py            # NEW - SAITS implementation (Phase 2)
    ├── brits_model.py            # NEW - BRITS implementation (Phase 2)
    ├── enhanced_unet.py          # NEW - Enhanced U-Net (Phase 3)
    ├── transformer_model.py      # NEW - Transformer implementation (Phase 4)
    ├── mrnn_model.py             # NEW - mRNN implementation (Phase 4)
    └── utils/                    # NEW - Utility functions
        ├── __init__.py
        ├── data_preparation.py   # NEW - PyPOTS data format utilities
        ├── model_validation.py   # NEW - Model validation utilities
        └── fallback_handler.py   # NEW - Graceful fallback mechanisms
```

**Implementation:**
```python
# models/advanced_models/__init__.py
"""
Advanced Deep Learning Models Module
Provides state-of-the-art models for well log imputation with graceful fallbacks
"""

# Safe imports with fallback handling
try:
    from .saits_model import SAITSModel
    SAITS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ SAITS model not available: {e}")
    SAITSModel = None
    SAITS_AVAILABLE = False

try:
    from .brits_model import BRITSModel
    BRITS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ BRITS model not available: {e}")
    BRITSModel = None
    BRITS_AVAILABLE = False

try:
    from .enhanced_unet import EnhancedUNet
    ENHANCED_UNET_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Enhanced UNet not available: {e}")
    EnhancedUNet = None
    ENHANCED_UNET_AVAILABLE = False

# Advanced models availability status
ADVANCED_MODELS_STATUS = {
    'saits': SAITS_AVAILABLE,
    'brits': BRITS_AVAILABLE,
    'enhanced_unet': ENHANCED_UNET_AVAILABLE,
}

ADVANCED_MODELS_AVAILABLE = any(ADVANCED_MODELS_STATUS.values())

if ADVANCED_MODELS_AVAILABLE:
    print(f"✅ Advanced models loaded: {[k for k, v in ADVANCED_MODELS_STATUS.items() if v]}")
else:
    print("⚠️ No advanced models available - check dependencies")

__all__ = ['SAITSModel', 'BRITSModel', 'EnhancedUNet', 'ADVANCED_MODELS_AVAILABLE', 'ADVANCED_MODELS_STATUS']
```

##### Task 1.4: Base Model Class Implementation
```python
# models/advanced_models/base_model.py
"""
Base class for advanced deep learning models
Provides common interface and utilities for PyPOTS-based models
"""

import torch
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

class BaseAdvancedModel(ABC):
    """
    Abstract base class for advanced deep learning models.
    Provides common interface and utilities for well log imputation.
    """
    
    def __init__(self, n_features: int = 4, sequence_len: int = 64, 
                 epochs: int = 50, batch_size: int = 32, **kwargs):
        """
        Initialize base model parameters.
        
        Args:
            n_features: Number of log features (GR, NPHI, RHOB, target)
            sequence_len: Length of input sequences (depth windows)
            epochs: Training epochs
            batch_size: Training batch size
            **kwargs: Additional model-specific parameters
        """
        self.n_features = n_features
        self.sequence_len = sequence_len
        self.epochs = epochs
        self.batch_size = batch_size
        self.is_fitted = False
        self.model = None
        
        # Store additional parameters
        self.model_params = kwargs
        
    @abstractmethod
    def _initialize_model(self) -> None:
        """Initialize the specific model architecture."""
        pass
    
    @abstractmethod
    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data in model-specific format."""
        pass
    
    def _prepare_pypots_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """
        Convert data to PyPOTS format.
        
        Args:
            data: Input data tensor (batch, sequence, features)
            truth_data: Ground truth data (optional, for training)
            
        Returns:
            Dictionary in PyPOTS format
        """
        if isinstance(data, torch.Tensor):
            data = data.cpu().numpy()
            
        if truth_data is not None:
            if isinstance(truth_data, torch.Tensor):
                truth_data = truth_data.cpu().numpy()
                
            # Create indicating mask (1 where data is observed, 0 where missing)
            indicating_mask = ~np.isnan(data)
            
            return {
                'X': data,
                'X_intact': truth_data,
                'indicating_mask': indicating_mask
            }
        else:
            # For prediction only
            indicating_mask = ~np.isnan(data)
            return {
                'X': data,
                'indicating_mask': indicating_mask
            }
    
    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor, 
            epochs: Optional[int] = None, batch_size: Optional[int] = None) -> None:
        """
        Train the model.
        
        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
        """
        if self.model is None:
            self._initialize_model()
            
        print(f"Training {self.__class__.__name__} for {epochs or self.epochs} epochs...")
        
        # Prepare training data
        train_set = self._prepare_data(train_data, truth_data)
        
        # Train the model
        self.model.fit(train_set)
        self.is_fitted = True
        
        print(f"{self.__class__.__name__} training completed!")
    
    def predict(self, data: torch.Tensor) -> torch.Tensor:
        """
        Predict/impute missing values.
        
        Args:
            data: Input data with missing values
            
        Returns:
            Imputed data tensor
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")
            
        # Prepare test data
        test_set = self._prepare_data(data)
        
        # Get predictions
        imputed_data = self.model.predict(test_set)
        
        # Convert back to tensor format
        return torch.from_numpy(imputed_data).float()
    
    def evaluate_imputation(self, original_data: torch.Tensor, 
                          imputed_data: torch.Tensor, 
                          missing_mask: torch.Tensor) -> Dict[str, float]:
        """
        Evaluate imputation performance.
        
        Args:
            original_data: Original data with missing values
            imputed_data: Imputed data
            missing_mask: Mask indicating originally missing values
            
        Returns:
            Dictionary with evaluation metrics
        """
        # Convert to numpy if needed
        if isinstance(original_data, torch.Tensor):
            original_data = original_data.cpu().numpy()
        if isinstance(imputed_data, torch.Tensor):
            imputed_data = imputed_data.cpu().numpy()
        if isinstance(missing_mask, torch.Tensor):
            missing_mask = missing_mask.cpu().numpy()
            
        # Flatten for evaluation
        original_flat = original_data.flatten()
        imputed_flat = imputed_data.flatten()
        missing_flat = missing_mask.flatten()
        
        # Evaluate only on originally non-missing values
        valid_mask = ~np.isnan(original_flat) & ~np.isnan(imputed_flat) & ~missing_flat
        
        if valid_mask.sum() == 0:
            return {'mae': float('inf'), 'rmse': float('inf'), 'r2': -float('inf')}
            
        y_true = original_flat[valid_mask]
        y_pred = imputed_flat[valid_mask]
        
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)
        
        return {'mae': mae, 'rmse': rmse, 'r2': r2}
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information and parameters."""
        return {
            'model_name': self.__class__.__name__,
            'n_features': self.n_features,
            'sequence_len': self.sequence_len,
            'epochs': self.epochs,
            'batch_size': self.batch_size,
            'is_fitted': self.is_fitted,
            'additional_params': self.model_params
        }
```

#### **Day 5-7: Safe Model Loading & Integration Framework**

##### Task 1.5: Enhanced Model Registry with Fallbacks
```python
# ml_core.py - Enhanced model loading section (ADDITION, not replacement)

# Add to existing imports
try:
    from models.advanced_models import (
        SAITSModel, BRITSModel, EnhancedUNet,
        ADVANCED_MODELS_AVAILABLE, ADVANCED_MODELS_STATUS
    )
    print("✅ Advanced deep learning models module loaded")
    if ADVANCED_MODELS_AVAILABLE:
        print(f"📊 Available advanced models: {[k for k, v in ADVANCED_MODELS_STATUS.items() if v]}")
    else:
        print("💡 Install PyPOTS and MONAI for advanced models: pip install pypots monai")
except ImportError as e:
    print(f"⚠️ Advanced deep learning models not available: {e}")
    ADVANCED_MODELS_AVAILABLE = False
    ADVANCED_MODELS_STATUS = {}
    SAITSModel = None
    BRITSModel = None
    EnhancedUNet = None

# Enhanced model availability checker
def check_model_dependencies():
    """Check and report model dependencies status."""
    status_report = {
        'basic_models': DEEP_MODELS_AVAILABLE,
        'advanced_models': ADVANCED_MODELS_AVAILABLE,
        'individual_models': ADVANCED_MODELS_STATUS if ADVANCED_MODELS_AVAILABLE else {},
        'recommendations': []
    }
    
    if not DEEP_MODELS_AVAILABLE:
        status_report['recommendations'].append("Install PyTorch for basic deep learning models")
    
    if not ADVANCED_MODELS_AVAILABLE:
        status_report['recommendations'].append("Install PyPOTS (pip install pypots) for advanced models")
        status_report['recommendations'].append("Install MONAI (pip install monai) for enhanced U-Net")
    
    return status_report

# Safe model getter with fallbacks
def get_model_class_safe(model_key: str):
    """
    Safely get model class with fallback handling.
    
    Args:
        model_key: Model identifier
        
    Returns:
        Model class or None if not available
    """
    if model_key in MODEL_REGISTRY:
        model_class = MODEL_REGISTRY[model_key]['model_class']
        if model_class is not None:
            return model_class
        else:
            print(f"⚠️ Model {model_key} not available - missing dependencies")
            return None
    else:
        print(f"❌ Unknown model key: {model_key}")
        return None

# Enhanced model recommendation system
def recommend_models_for_task(task_requirements: Dict[str, Any]) -> List[str]:
    """
    Recommend models based on task requirements with availability checking.
    
    Args:
        task_requirements: dict with keys like 'accuracy_priority', 'speed_priority', 'data_complexity'
        
    Returns:
        list: Recommended model keys in priority order (only available models)
    """
    all_recommendations = []
    
    # High accuracy priority
    if task_requirements.get('accuracy_priority', False):
        if ADVANCED_MODELS_AVAILABLE:
            if ADVANCED_MODELS_STATUS.get('saits', False):
                all_recommendations.append('saits')
            if ADVANCED_MODELS_STATUS.get('brits', False):
                all_recommendations.append('brits')
            if ADVANCED_MODELS_STATUS.get('enhanced_unet', False):
                all_recommendations.append('enhanced_unet')
    
    # Speed priority
    if task_requirements.get('speed_priority', False):
        if DEEP_MODELS_AVAILABLE:
            all_recommendations.extend(['autoencoder', 'unet'])
    
    # Balanced approach (default)
    if task_requirements.get('balanced', True):
        if ADVANCED_MODELS_AVAILABLE:
            if ADVANCED_MODELS_STATUS.get('brits', False):
                all_recommendations.append('brits')
            if ADVANCED_MODELS_STATUS.get('enhanced_unet', False):
                all_recommendations.append('enhanced_unet')
        if DEEP_MODELS_AVAILABLE:
            all_recommendations.append('autoencoder')
    
    # Remove duplicates while preserving order and filter available models
    seen = set()
    available_recommendations = []
    for model in all_recommendations:
        if model not in seen and model in MODEL_REGISTRY:
            model_class = MODEL_REGISTRY[model]['model_class']
            if model_class is not None:  # Only include available models
                seen.add(model)
                available_recommendations.append(model)
    
    return available_recommendations
```

### 🧪 **Phase 1 Testing & Validation**

#### **Comprehensive Testing Suite**
```python
# tests/test_phase1_foundation.py
import unittest
import torch
import numpy as np
from models.advanced_models import ADVANCED_MODELS_AVAILABLE, ADVANCED_MODELS_STATUS

class TestPhase1Foundation(unittest.TestCase):
    """Test suite for Phase 1 foundation setup."""
    
    def test_dependency_installation(self):
        """Test that all required dependencies are properly installed."""
        try:
            import pypots
            self.assertTrue(True, "PyPOTS installed successfully")
        except ImportError:
            self.skipTest("PyPOTS not installed - expected for basic setup")
        
        try:
            import monai
            self.assertTrue(True, "MONAI installed successfully")
        except ImportError:
            self.skipTest("MONAI not installed - expected for basic setup")
    
    def test_backward_compatibility(self):
        """Test that existing models still work after new installations."""
        from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet
        
        # Test SimpleAutoencoder
        model = SimpleAutoencoder(n_features=4, sequence_len=32, epochs=1)
        test_data = torch.randn(5, 32, 4)
        test_data[test_data > 0.5] = float('nan')
        truth_data = torch.randn(5, 32, 4)
        
        model.fit(test_data, truth_data, epochs=1, batch_size=2)
        predictions = model.predict(test_data)
        
        self.assertEqual(predictions.shape, test_data.shape)
        self.assertFalse(torch.isnan(predictions).all())
    
    def test_safe_model_loading(self):
        """Test that model loading handles missing dependencies gracefully."""
        from ml_core import get_model_class_safe, check_model_dependencies
        
        # Test dependency status check
        status = check_model_dependencies()
        self.assertIn('basic_models', status)
        self.assertIn('advanced_models', status)
        self.assertIn('recommendations', status)
        
        # Test safe model loading
        autoencoder_class = get_model_class_safe('autoencoder')
        if autoencoder_class is not None:
            self.assertTrue(callable(autoencoder_class))
    
    def test_directory_structure(self):
        """Test that new directory structure is properly created."""
        import os
        
        # Check advanced_models directory exists
        self.assertTrue(os.path.exists('models/advanced_models'))
        self.assertTrue(os.path.exists('models/advanced_models/__init__.py'))
        
        # Check utils directory exists
        self.assertTrue(os.path.exists('models/advanced_models/utils'))
        self.assertTrue(os.path.exists('models/advanced_models/utils/__init__.py'))

if __name__ == '__main__':
    unittest.main()
```

### 📊 **Phase 1 Success Criteria**

#### **Technical Validation**
- [ ] All new dependencies installed without conflicts
- [ ] Existing SimpleAutoencoder and SimpleUNet work unchanged
- [ ] Advanced models directory structure created
- [ ] Safe model loading framework implemented
- [ ] Graceful fallback mechanisms working
- [ ] No system instability or crashes

#### **Code Quality Validation**
- [ ] All new code follows existing style conventions
- [ ] Comprehensive error handling implemented
- [ ] Clear logging and user feedback messages
- [ ] Documentation strings for all new functions/classes

#### **User Experience Validation**
- [ ] Clear status messages about model availability
- [ ] Helpful recommendations for missing dependencies
- [ ] No disruption to existing workflow
- [ ] Informative error messages with solutions

### 🚀 **Phase 1 Deliverables**

1. **Enhanced Environment**: PyPOTS and MONAI dependencies installed and tested
2. **Directory Structure**: Complete advanced_models module structure
3. **Base Framework**: BaseAdvancedModel class and utilities
4. **Safe Loading**: Enhanced model registry with fallback mechanisms
5. **Testing Suite**: Comprehensive tests for backward compatibility
6. **Documentation**: Updated README with new dependencies and structure

### ➡️ **Transition to Phase 2**

**Prerequisites for Phase 2:**
- [ ] All Phase 1 success criteria met
- [ ] PyPOTS successfully installed and tested
- [ ] Base model framework validated
- [ ] Existing models confirmed working

**Phase 2 Preview:**
- Implement SAITS model (Priority 1)
- Implement BRITS model (Priority 2)
- Add models to registry with fallbacks
- Initial performance benchmarking

---

**Estimated Time**: 5-7 days
**Risk Level**: Low (non-disruptive additions only)
**Dependencies**: PyTorch (existing), PyPOTS, MONAI
