# GPU Process Archive

This directory contains test files and standalone scripts that were moved from the main pipeline directory to keep the project structure clean and organized.

## Archive Date
**Date**: 2025-07-08
**Reason**: Codebase cleanup to remove obsolete test files and standalone scripts

## Contents

### Obsolete Test Files
These test files were standalone development/debugging scripts that are not part of the current active testing workflow:

- `test_advanced_models_gpu.py` - GPU testing for advanced models
- `test_data_leakage_fix.py` - Data leakage fix testing  
- `test_gpu_optimization.py` - GPU optimization testing
- `test_implementation_quick.py` - Quick implementation testing
- `test_memory_optimization.py` - Memory optimization testing
- `test_postprocessing_simple.py` - Simple postprocessing testing
- `test_prediction_fix.py` - Prediction fix testing
- `test_saits_postprocessing_fix.py` - SAITS postprocessing testing
- `test_sequence_fix.py` - Sequence fix testing
- `test_temporal_fixes.py` - Temporal fixes testing
- `test_xgboost_gpu_config.py` - XGBoost GPU configuration testing
- `simple_data_leakage_test.py` - Simple data leakage testing
- `simple_test_fix.py` - Simple test fixes

### Why These Files Were Archived

1. **Not imported by main pipeline**: These files are standalone scripts that are not imported by any of the core pipeline modules (main.py, ml_core.py, data_handler.py, etc.)

2. **Development/debugging purpose**: These appear to be development and debugging scripts created during various phases of the project

3. **Superseded by current testing**: The active testing framework is now in `tests/performance_benchmarking.py` and the archived test files in `archives/test_files/`

4. **No references in documentation**: These files are not referenced in the current documentation or configuration files

## Active Files That Were NOT Moved

The following files remain in the main directory as they are actively used:

### Core Pipeline Files
- `main.py` - Main entry point (imports from all core modules)
- `data_handler.py` - Core data processing
- `config_handler.py` - Configuration management
- `ml_core.py` - Core ML functionality
- `reporting.py` - Results and visualization
- `enhanced_preprocessing.py` - Advanced preprocessing (imported by data_handler)
- `temporal_validation.py` - Temporal validation utilities
- `data_leakage_detector.py` - Data leakage detection
- `setup_gpu_environment.py` - GPU environment setup

### Active Directories
- `models/` - All model implementations (actively imported by ml_core)
- `utils/` - Utility modules (actively imported by ml_core)
- `tests/` - Contains active performance benchmarking

## Usage

These archived test files can still be run individually if needed for debugging or regression testing:

```bash
# Run individual archived test
python archives/gpu_process/test_implementation_quick.py

# Run all archived tests (if using pytest)
pytest archives/gpu_process/
```

## Notes

- All archived files maintain their original functionality
- Files were moved to improve project organization and reduce clutter
- The main pipeline functionality is unaffected by this archival
- These files represent various development phases and debugging efforts
- If any of these files are needed in the future, they can be moved back to the main directory

## Archive Structure

```
archives/
├── gpu_process/         # This directory - GPU process development files
├── test_files/          # Previously archived test files from earlier phases
└── second_stage/        # Previous archive of second stage development files
```
