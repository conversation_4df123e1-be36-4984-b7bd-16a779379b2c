"""
BROADCASTING ERROR FIX - DIRECT SOLUTION

The issue is in the evaluate_imputation_and_prediction function where arrays
with different shapes are being compared after flattening.

PROBLEM LINES:
- Line 1332: y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy().flatten()
- Line 1333: y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy().flatten()

SOLUTION:
1. Remove .flatten() from both lines
2. Use the safe_array_alignment function to ensure compatible shapes
3. Add proper error handling

MANUAL FIX INSTRUCTIONS:
"""

# Step 1: Replace line 1332
# FROM: y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy().flatten()
# TO:   y_pred_prediction = predicted_sequences[:, :, target_idx].detach().cpu().numpy()

# Step 2: Replace line 1333  
# FROM: y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy().flatten()
# TO:   y_true_prediction = test_true_tensor[:, :, target_idx].detach().cpu().numpy()

# Step 3: Add after line 1333 (new lines):
# 
#         # Safe array alignment to prevent broadcasting errors
#         y_pred_prediction, y_true_prediction = safe_array_alignment(
#             y_pred_prediction, y_true_prediction, "prediction evaluation"
#         )

# Step 4: Modify the NaN removal section (around line 1336)
# FROM: valid_idx = ~np.isnan(y_true_prediction) & ~np.isnan(y_pred_prediction)
# TO:   if len(y_pred_prediction) > 0 and len(y_true_prediction) > 0:
#           valid_idx = ~np.isnan(y_true_prediction) & ~np.isnan(y_pred_prediction)
#       else:
#           print("⚠️ No valid prediction samples after alignment")

print("Manual fix required:")
print("1. Edit ml_core.py around lines 1332-1336")
print("2. Remove .flatten() calls")  
print("3. Add safe_array_alignment call")
print("4. Add length checks")
print("\nThis will fix the broadcasting error for SAITS and BRITS models.")