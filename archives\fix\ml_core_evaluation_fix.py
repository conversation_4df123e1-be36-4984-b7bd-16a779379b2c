# Modifications to ml_core.py impute_logs_deep function
# 
# This code should replace the evaluation section in impute_logs_deep (around lines 1279-1343)
# to properly evaluate both imputation and prediction performance

# Add this import at the top of ml_core.py:
# from deep_learning_evaluation_fix import evaluate_imputation_and_prediction

def impute_logs_deep_fixed_evaluation(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing=True):
    """
    Fixed version of impute_logs_deep with proper evaluation for both tasks.
    
    Key changes:
    1. Separates imputation evaluation (on artificial gaps) from prediction evaluation
    2. Uses held-out test data for realistic prediction assessment
    3. Reports both metrics transparently
    """
    
    # ... [Keep all existing code up to line 1279] ...
    
    # 4. ENHANCED EVALUATION PHASE - Fixed for proper assessment
    print("📊 Enhanced Evaluation Phase...")
    
    # Import the evaluation function
    from deep_learning_evaluation_fix import evaluate_imputation_and_prediction
    
    # Perform comprehensive evaluation
    eval_results_comprehensive = evaluate_imputation_and_prediction(
        model=model,
        val_sequences_missing=val_train_tensor,
        val_sequences_true=val_truth_tensor,
        test_df=test_df,  # This is the held-out test set from the split
        feature_cols=feature_cols,
        target_col=target_col,
        all_features=all_features,
        scalers=scalers,
        sequence_len=hparams['sequence_len'],
        use_enhanced_preprocessing=use_enhanced_preprocessing
    )
    
    # Extract metrics for backward compatibility
    if eval_results_comprehensive['imputation_metrics']:
        # Use imputation metrics for models that are imputation-focused
        mae = eval_results_comprehensive['imputation_metrics']['mae']
        r2 = eval_results_comprehensive['imputation_metrics']['r2']
        rmse = eval_results_comprehensive['imputation_metrics']['rmse']
    else:
        mae, r2, rmse = -1, -1, -1
    
    # Calculate composite score
    r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))
    composite_score = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)
    
    # Display warning if there's a significant R² discrepancy
    if (eval_results_comprehensive['imputation_metrics'] and 
        eval_results_comprehensive['prediction_metrics']):
        
        r2_imp = eval_results_comprehensive['imputation_metrics']['r2']
        r2_pred = eval_results_comprehensive['prediction_metrics']['r2']
        r2_discrepancy = r2_imp - r2_pred
        
        if r2_discrepancy > 0.2:
            print("\n" + "="*60)
            print("⚠️  PERFORMANCE DISCREPANCY WARNING")
            print("="*60)
            print(f"This model shows significantly different performance between:")
            print(f"  • Imputation (filling gaps): R² = {r2_imp:.4f}")
            print(f"  • Prediction (no context):    R² = {r2_pred:.4f}")
            print(f"  • Discrepancy:                    {r2_discrepancy:.4f}")
            print("\nRECOMMENDATION:")
            print("  - Use this model for imputation tasks only")
            print("  - Consider XGBoost/CatBoost/LightGBM for prediction tasks")
            print("="*60 + "\n")
    
    # ... [Keep existing code from line 1344 onwards for prediction phase] ...
    
    # At the end, return enhanced results
    return res_df, {
        'target': target_col,
        'evaluations': [{
            'mae': mae,
            'r2': r2,
            'rmse': rmse,
            'model_name': model_config['name'],
            'composite_score': composite_score,
            # Add new comprehensive metrics
            'comprehensive_evaluation': eval_results_comprehensive
        }],
        'best_model_name': model_config['name'],
        'trained_models': {model_config['name']: model}
    }


# Alternative: Simpler inline fix for the existing evaluation section
# This can be directly inserted to replace lines 1279-1343 in ml_core.py

# === REPLACEMENT CODE FOR LINES 1279-1343 ===

    # 4. Evaluation on Validation Set - ENHANCED FOR PROPER EVALUATION
    print("📊 Evaluation phase...")
    
    # Store both imputation and prediction metrics
    imputation_metrics = {'mae': -1, 'r2': -1, 'rmse': -1}
    prediction_metrics = {'mae': -1, 'r2': -1, 'rmse': -1}
    
    if val_train_tensor.shape[0] > 0:
        # === IMPUTATION EVALUATION (existing code) ===
        # Use memory-optimized prediction for validation if available
        if hasattr(model, 'predict_large_dataset'):
            imputed_val_tensor = model.predict_large_dataset(val_train_tensor)
        else:
            imputed_val_tensor = model.predict(val_train_tensor)
        target_idx = all_features.index(target_col)

        # Only evaluate on artificially missing values
        val_mask = torch.isnan(val_train_tensor[:, :, target_idx])
        
        if val_mask.any():
            # Extract predictions and ground truth for artificially missing values only
            y_pred_val = imputed_val_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()
            y_true_val = val_truth_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()

            # Additional validation: ensure we have valid data
            valid_indices = ~np.isnan(y_true_val) & ~np.isnan(y_pred_val)
            y_pred_val = y_pred_val[valid_indices]
            y_true_val = y_true_val[valid_indices]

            if len(y_true_val) > 0:
                imputation_metrics['mae'] = mean_absolute_error(y_true_val, y_pred_val)
                imputation_metrics['r2'] = r2_score(y_true_val, y_pred_val)
                imputation_metrics['rmse'] = np.sqrt(mean_squared_error(y_true_val, y_pred_val))
                
                print(f"✅ Imputation Metrics (Artificial Missing Values):")
                print(f"   • MAE: {imputation_metrics['mae']:.4f}")
                print(f"   • R²: {imputation_metrics['r2']:.4f}")
                print(f"   • RMSE: {imputation_metrics['rmse']:.4f}")
                print(f"   • Evaluated Points: {len(y_true_val)}")
        
        # === PREDICTION EVALUATION (new code) ===
        # Create sequences where entire target column is missing
        val_df_pred = val_df_scaled.copy()
        val_df_pred[target_col] = np.nan  # Mask entire target
        
        val_pred_sequences, _ = create_sequences(val_df_pred, 'WELL', all_features,
                                                 sequence_len=hparams['sequence_len'], 
                                                 use_enhanced=use_enhanced_preprocessing)
        
        if val_pred_sequences.shape[0] > 0:
            val_pred_tensor = torch.from_numpy(val_pred_sequences.astype(np.float32))
            
            # Get predictions
            if hasattr(model, 'predict_large_dataset'):
                pred_output = model.predict_large_dataset(val_pred_tensor)
            else:
                pred_output = model.predict(val_pred_tensor)
            
            # Compare with ground truth
            y_pred_full = pred_output[:, :, target_idx].detach().cpu().numpy().flatten()
            y_true_full = val_sequences_true[:, :, target_idx].flatten()
            
            # Remove NaNs
            valid_idx = ~np.isnan(y_true_full) & ~np.isnan(y_pred_full)
            y_pred_full = y_pred_full[valid_idx]
            y_true_full = y_true_full[valid_idx]
            
            if len(y_true_full) > 0:
                prediction_metrics['mae'] = mean_absolute_error(y_true_full, y_pred_full)
                prediction_metrics['r2'] = r2_score(y_true_full, y_pred_full)
                prediction_metrics['rmse'] = np.sqrt(mean_squared_error(y_true_full, y_pred_full))
                
                print(f"\n✅ Prediction Metrics (No Target Context):")
                print(f"   • MAE: {prediction_metrics['mae']:.4f}")
                print(f"   • R²: {prediction_metrics['r2']:.4f}")
                print(f"   • RMSE: {prediction_metrics['rmse']:.4f}")
                print(f"   • Evaluated Points: {len(y_true_full)}")
    
    # Analyze and report discrepancy
    if imputation_metrics['r2'] > 0 and prediction_metrics['r2'] > 0:
        r2_discrepancy = imputation_metrics['r2'] - prediction_metrics['r2']
        
        if r2_discrepancy > 0.2:
            print(f"\n🚨 PERFORMANCE DISCREPANCY DETECTED!")
            print(f"   • Imputation R²: {imputation_metrics['r2']:.4f}")
            print(f"   • Prediction R²: {prediction_metrics['r2']:.4f}")
            print(f"   • Discrepancy: {r2_discrepancy:.4f}")
            print(f"\n   ⚠️ This model is optimized for imputation, not prediction.")
            print(f"   Consider using shallow ML models for pure prediction tasks.")
    
    # Use imputation metrics for backward compatibility
    # but store both for comprehensive reporting
    mae = imputation_metrics['mae']
    r2 = imputation_metrics['r2']
    rmse = imputation_metrics['rmse']
    
    # Enhanced composite score calculation
    r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))
    composite_score = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)

# === END REPLACEMENT CODE ===