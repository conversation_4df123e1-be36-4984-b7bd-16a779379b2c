#!/usr/bin/env python3
"""
Simple test to verify the sequence creation fix works.
"""

import numpy as np
import pandas as pd

# Test the core logic without imports
def test_core_logic():
    """Test the core prediction mode detection logic."""
    print("🧪 Testing Core Logic")
    print("=" * 30)
    
    # Create test data
    np.random.seed(42)
    n_points = 100
    
    # Create synthetic data
    df = pd.DataFrame({
        'WELL': ['A'] * n_points,
        'MD': np.linspace(1000, 1100, n_points),
        'GR': np.random.normal(50, 10, n_points),
        'RHOB': np.random.normal(2.3, 0.1, n_points),
        'NPHI': np.random.normal(0.15, 0.05, n_points),
        'P-WAVE': np.random.normal(8000, 500, n_points)
    })
    
    # Add some missing values to P-WAVE (training scenario)
    missing_indices = np.random.choice(n_points, size=int(0.3 * n_points), replace=False)
    df.loc[missing_indices, 'P-WAVE'] = np.nan
    
    print(f"Training data created: {df.shape}")
    print(f"P-WAVE missing: {df['P-WAVE'].isna().sum()} / {len(df)} ({df['P-WAVE'].isna().mean()*100:.1f}%)")
    
    # Test training mode detection
    feature_cols = ['GR', 'RHOB', 'NPHI', 'P-WAVE']
    target_col_idx = len(feature_cols) - 1  # P-WAVE is last
    
    well_data = df[feature_cols].values
    target_nan_ratio = np.isnan(well_data[:, target_col_idx]).mean()
    is_prediction_mode = target_nan_ratio > 0.95
    
    print(f"Training mode - Target NaN ratio: {target_nan_ratio:.3f}")
    print(f"Training mode - Is prediction mode: {is_prediction_mode}")
    
    # Test prediction mode (mask entire target column)
    df_pred = df.copy()
    df_pred['P-WAVE'] = np.nan  # Mask entire target
    
    well_data_pred = df_pred[feature_cols].values
    target_nan_ratio_pred = np.isnan(well_data_pred[:, target_col_idx]).mean()
    is_prediction_mode_pred = target_nan_ratio_pred > 0.95
    
    print(f"Prediction mode - Target NaN ratio: {target_nan_ratio_pred:.3f}")
    print(f"Prediction mode - Is prediction mode: {is_prediction_mode_pred}")
    
    # Test valid interval detection logic
    print(f"\n🔧 Testing Valid Interval Detection")
    
    # Training mode: check all columns
    all_valid_training = ~np.isnan(well_data).any(axis=1)
    valid_count_training = all_valid_training.sum()
    print(f"Training mode - Valid rows (all columns): {valid_count_training} / {len(well_data)}")
    
    # Prediction mode: check only feature columns (exclude target)
    feature_only_data = well_data_pred[:, :-1]  # Exclude last column (target)
    all_valid_prediction = ~np.isnan(feature_only_data).any(axis=1)
    valid_count_prediction = all_valid_prediction.sum()
    print(f"Prediction mode - Valid rows (features only): {valid_count_prediction} / {len(well_data_pred)}")
    
    # Results
    training_success = valid_count_training > 0
    prediction_success = valid_count_prediction > 0
    
    print(f"\n📋 Results:")
    print(f"   Training mode valid intervals: {'✅ FOUND' if training_success else '❌ NONE'}")
    print(f"   Prediction mode valid intervals: {'✅ FOUND' if prediction_success else '❌ NONE'}")
    
    if training_success and prediction_success:
        print(f"\n🎉 SUCCESS: Core logic is working correctly!")
        print(f"   - Training mode correctly identifies {valid_count_training} valid rows")
        print(f"   - Prediction mode correctly identifies {valid_count_prediction} valid rows")
        print(f"   - The fix should resolve the sequence creation issue")
        return True
    else:
        print(f"\n❌ FAILURE: Core logic has issues")
        return False

if __name__ == "__main__":
    success = test_core_logic()
    if success:
        print("\n✅ The sequence creation fix logic is correct!")
    else:
        print("\n❌ The fix logic needs revision!")
