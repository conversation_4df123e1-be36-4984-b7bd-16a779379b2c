#!/usr/bin/env python3
"""
Test script to verify SAITS model fixes for the unpacking error.
This script tests the enhanced error handling and validation.
"""

import sys
import os
import numpy as np
import pandas as pd
import torch
import warnings

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """Create synthetic well log data for testing."""
    print("📊 Creating synthetic test data...")
    
    # Create synthetic well log data
    np.random.seed(42)
    n_samples = 200
    
    wells = ['WELL_A', 'WELL_B']
    all_data = []
    
    for well in wells:
        depths = np.linspace(1000, 2000, n_samples)
        
        # Create realistic well log curves
        gr = 50 + 30 * np.sin(depths / 100) + np.random.normal(0, 5, n_samples)
        nphi = 0.2 + 0.1 * np.cos(depths / 150) + np.random.normal(0, 0.02, n_samples)
        rhob = 2.3 + 0.2 * np.sin(depths / 200) + np.random.normal(0, 0.05, n_samples)
        pwave = 100 + 20 * np.cos(depths / 120) + np.random.normal(0, 3, n_samples)
        
        well_data = pd.DataFrame({
            'WELL': well,
            'MD': depths,
            'GR': gr,
            'NPHI': nphi,
            'RHOB': rhob,
            'P-WAVE': pwave
        })
        
        all_data.append(well_data)
    
    df = pd.concat(all_data, ignore_index=True)
    
    # Introduce some missing values in P-WAVE (target)
    missing_indices = np.random.choice(len(df), size=int(0.3 * len(df)), replace=False)
    df.loc[missing_indices, 'P-WAVE'] = np.nan
    
    print(f"✅ Created test data: {df.shape}")
    print(f"   Wells: {df['WELL'].unique()}")
    print(f"   Missing P-WAVE: {df['P-WAVE'].isna().sum()} / {len(df)} ({df['P-WAVE'].isna().mean()*100:.1f}%)")
    
    return df

def test_data_preparation():
    """Test the enhanced data preparation with error handling."""
    print("\n🔧 Testing enhanced data preparation...")
    
    try:
        from data_handler import create_sequences
        
        df = create_test_data()
        feature_cols = ['GR', 'NPHI', 'RHOB', 'P-WAVE']
        
        # Test with enhanced preprocessing
        print("   Testing enhanced preprocessing...")
        sequences_enhanced, metadata_enhanced = create_sequences(
            df, 'WELL', feature_cols, sequence_len=32, use_enhanced=True
        )
        
        print(f"   Enhanced sequences: {sequences_enhanced.shape}")
        print(f"   Enhanced metadata: {len(metadata_enhanced)}")
        
        # Test with standard preprocessing
        print("   Testing standard preprocessing...")
        sequences_standard, metadata_standard = create_sequences(
            df, 'WELL', feature_cols, sequence_len=32, use_enhanced=False
        )
        
        print(f"   Standard sequences: {sequences_standard.shape}")
        print(f"   Standard metadata: {len(metadata_standard)}")
        
        return sequences_enhanced, metadata_enhanced
        
    except Exception as e:
        print(f"❌ Data preparation test failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_saits_model():
    """Test the SAITS model with enhanced error handling."""
    print("\n🤖 Testing SAITS model...")
    
    try:
        from models.advanced_models.saits_model import SAITSModel
        
        # Create test data
        sequences, metadata = test_data_preparation()
        
        if sequences is None or sequences.shape[0] == 0:
            print("❌ Cannot test SAITS model - no sequences available")
            return False
        
        # Create training and truth data
        train_sequences = sequences.copy()
        truth_sequences = sequences.copy()
        
        # Introduce artificial missing values in training data
        missing_mask = np.random.random(train_sequences.shape) < 0.2
        train_sequences[missing_mask] = np.nan
        
        # Convert to tensors
        train_tensor = torch.from_numpy(train_sequences).float()
        truth_tensor = torch.from_numpy(truth_sequences).float()
        
        print(f"   Train tensor shape: {train_tensor.shape}")
        print(f"   Truth tensor shape: {truth_tensor.shape}")
        
        # Initialize SAITS model with small parameters for testing
        model = SAITSModel(
            n_features=4,
            sequence_len=32,
            n_layers=1,  # Small for testing
            d_model=64,  # Small for testing
            n_heads=2,   # Small for testing
            epochs=2,    # Very few epochs for testing
            batch_size=8,
            learning_rate=1e-3,
            device='cpu'  # Use CPU for testing
        )
        
        print("✅ SAITS model initialized successfully")
        
        # Test training
        print("   Testing training...")
        model.fit(train_tensor, truth_tensor)
        
        print("✅ SAITS training completed successfully")
        
        # Test prediction
        print("   Testing prediction...")
        predictions = model.predict(train_tensor)
        
        print(f"✅ SAITS prediction completed: {predictions.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ SAITS model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenarios():
    """Test various error scenarios to ensure proper handling."""
    print("\n🧪 Testing error scenarios...")
    
    try:
        from models.advanced_models.saits_model import SAITSModel
        
        # Test 1: Invalid parameters
        print("   Test 1: Invalid parameters...")
        try:
            model = SAITSModel(
                n_features=0,  # Invalid
                sequence_len=32,
                device='cpu'
            )
            print("❌ Should have failed with invalid n_features")
        except Exception as e:
            print(f"✅ Correctly caught invalid parameters: {type(e).__name__}")
        
        # Test 2: Mismatched tensor shapes
        print("   Test 2: Mismatched tensor shapes...")
        try:
            model = SAITSModel(n_features=4, sequence_len=32, epochs=1, device='cpu')
            train_tensor = torch.randn(10, 32, 4)
            truth_tensor = torch.randn(10, 32, 3)  # Wrong number of features
            model.fit(train_tensor, truth_tensor)
            print("❌ Should have failed with mismatched shapes")
        except Exception as e:
            print(f"✅ Correctly caught shape mismatch: {type(e).__name__}")
        
        # Test 3: All NaN data
        print("   Test 3: All NaN data...")
        try:
            model = SAITSModel(n_features=4, sequence_len=32, epochs=1, device='cpu')
            train_tensor = torch.full((10, 32, 4), float('nan'))
            truth_tensor = torch.randn(10, 32, 4)
            model.fit(train_tensor, truth_tensor)
            print("❌ Should have failed with all NaN data")
        except Exception as e:
            print(f"✅ Correctly caught all NaN data: {type(e).__name__}")
        
        print("✅ Error scenario tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Error scenario tests failed: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Starting SAITS fix verification tests...")
    print("=" * 60)
    
    # Suppress warnings for cleaner output
    warnings.filterwarnings('ignore')
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Data preparation
    if test_data_preparation()[0] is not None:
        success_count += 1
        print("✅ Test 1 PASSED: Data preparation")
    else:
        print("❌ Test 1 FAILED: Data preparation")
    
    # Test 2: SAITS model
    if test_saits_model():
        success_count += 1
        print("✅ Test 2 PASSED: SAITS model")
    else:
        print("❌ Test 2 FAILED: SAITS model")
    
    # Test 3: Error scenarios
    if test_error_scenarios():
        success_count += 1
        print("✅ Test 3 PASSED: Error scenarios")
    else:
        print("❌ Test 3 FAILED: Error scenarios")
    
    print("=" * 60)
    print(f"🏁 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All tests passed! SAITS fixes are working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
