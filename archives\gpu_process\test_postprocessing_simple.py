#!/usr/bin/env python3
"""
Simple test to verify the post-processing fix without running the full SAITS model.
This test simulates the post-processing step with mock data.
"""

import numpy as np
import pandas as pd
import sys
import os

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_postprocessing_fix():
    """Test the post-processing fix with simulated data."""
    print("🧪 Testing Post-processing Fix with Simulated Data...")
    
    # Create mock data that simulates the SAITS output
    n_sequences = 5
    sequence_len = 8
    n_features = 5
    
    # Simulate the imputed_sequences array (output from SAITS model)
    np.random.seed(42)
    imputed_sequences = np.random.randn(n_sequences, sequence_len, n_features).astype(np.float32)
    
    # Create mock metadata
    metadata = []
    for i in range(n_sequences):
        start_idx = i * 6  # Overlapping sequences
        original_indices = list(range(start_idx, start_idx + sequence_len))
        metadata.append({
            'well': f'WELL_{i % 3 + 1}',
            'original_indices': original_indices
        })
    
    # Create mock dataframe
    total_points = max([max(meta['original_indices']) for meta in metadata]) + 1
    df_index = pd.RangeIndex(total_points)
    all_features = ['GR', 'RHOB', 'NPHI', 'RT', 'PWAVE']
    
    print(f"Mock data created:")
    print(f"  - Sequences: {imputed_sequences.shape}")
    print(f"  - Total dataframe points: {total_points}")
    print(f"  - Features: {all_features}")
    
    # Test the post-processing logic
    try:
        # Create placeholder for averaging predictions on overlapping sequences
        pred_sum_df = pd.DataFrame(0.0, index=df_index, columns=all_features)
        pred_count_df = pd.DataFrame(0, index=df_index, columns=all_features)
        
        print(f"\nTesting post-processing loop...")
        
        for i, seq_meta in enumerate(metadata):
            original_indices = seq_meta['original_indices']
            predicted_sequence_scaled = imputed_sequences[i]
            
            print(f"  Processing sequence {i}:")
            print(f"    - Shape: {predicted_sequence_scaled.shape}")
            print(f"    - Indices: {original_indices}")
            
            # Test our fix
            try:
                # Convert numpy array to DataFrame with proper indexing
                predicted_df = pd.DataFrame(
                    predicted_sequence_scaled, 
                    index=original_indices, 
                    columns=all_features,
                    dtype=np.float64
                )
                
                # Use explicit DataFrame addition
                pred_sum_df.loc[original_indices, all_features] = (
                    pred_sum_df.loc[original_indices, all_features].add(predicted_df, fill_value=0.0)
                )
                pred_count_df.loc[original_indices, all_features] = (
                    pred_count_df.loc[original_indices, all_features] + 1
                )
                
                print(f"    ✅ Sequence {i} processed successfully")
                
            except Exception as e:
                print(f"    ❌ Error processing sequence {i}: {e}")
                return False
        
        # Test averaging
        print(f"\nTesting averaging...")
        avg_pred_df_scaled = pred_sum_df.divide(pred_count_df)
        
        print(f"  - pred_sum_df non-zero elements: {(pred_sum_df != 0).sum().sum()}")
        print(f"  - pred_count_df non-zero elements: {(pred_count_df != 0).sum().sum()}")
        print(f"  - avg_pred_df_scaled non-NaN elements: {avg_pred_df_scaled.notna().sum().sum()}")
        print(f"  - Max count value: {pred_count_df.max().max()}")
        
        # Test inverse scaling simulation
        print(f"\nTesting inverse scaling simulation...")
        from sklearn.preprocessing import StandardScaler
        
        # Create mock scalers
        scalers = {}
        for col in all_features:
            scaler = StandardScaler()
            # Fit with some dummy data
            dummy_data = np.random.randn(100, 1)
            scaler.fit(dummy_data)
            scalers[col] = scaler
        
        # Test inverse transform
        imputed_df_scaled = avg_pred_df_scaled.copy()
        for col in all_features:
            valid_mask = imputed_df_scaled[col].notna()
            if valid_mask.any():
                col_data = imputed_df_scaled.loc[valid_mask, [col]].values
                # Test our fix for inverse transform
                inverse_transformed = scalers[col].inverse_transform(col_data)
                if inverse_transformed.ndim > 1:
                    inverse_transformed = inverse_transformed.flatten()
                imputed_df_scaled.loc[valid_mask, col] = inverse_transformed
        
        print(f"  ✅ Inverse scaling completed successfully")
        
        print(f"\n✅ All post-processing tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Post-processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the simple post-processing test."""
    print("=" * 60)
    print(" SIMPLE POST-PROCESSING FIX TEST")
    print("=" * 60)
    
    success = test_postprocessing_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ POST-PROCESSING FIX VERIFIED - Ready for production!")
    else:
        print("❌ POST-PROCESSING FIX FAILED - Needs more work")
    print("=" * 60)

if __name__ == "__main__":
    main()
